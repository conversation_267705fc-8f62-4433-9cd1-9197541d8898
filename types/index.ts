export type MessageStatus = "pending" | "sent" | "failed"

export type MessageType = "text" | "component-reference"

export type Message = {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: Date
  status: MessageStatus
  messageType: MessageType
  // 为将来的组件节点引用功能预留
  componentId?: string
}

// 项目配置类型（简化版）
export type ProjectConfig = {
  promptTemplateId?: string
}

// 项目类型
export type Project = {
  id: string
  name: string
  description?: string
  createdAt: Date
  updatedAt: Date
  config: ProjectConfig
}

// 提示词模板类型（简化版）
export type PromptTemplate = {
  id: string
  name: string
  description?: string
  content: string
  isDefault?: boolean
  createdAt: Date
  updatedAt: Date
}

export type TreeNode = {
  id: string
  name: string
  type: "folder" | "file"
  children?: TreeNode[]
  expanded?: boolean
}

export type UploadedImage = {
  id: string
  file?: File
  name?: string
  url: string
  fileName?: string
  originalName?: string
  size: number
  type: string
  uploadedAt: string | Date
  base64?: string
}

export type UploadError = {
  type: "size" | "format" | "upload"
  message: string
}

export type ChatState = {
  messages: Message[]
  isLoading: boolean
  error: string | null
}

export type ChatActions = {
  addMessage: (content: string, role: "user" | "assistant", messageType?: MessageType) => string
  updateMessageStatus: (messageId: string, status: MessageStatus) => void
  clearMessages: () => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  sendToLLM: (request: GenerationRequest) => Promise<any>
  sendToLLMStream: (request: GenerationRequest, onChunk?: (chunk: string) => void) => Promise<string>
}

// 项目状态管理类型
export type ProjectState = {
  projects: Project[]
  currentProject: Project | null
  isLoading: boolean
  error: string | null
}

export type ProjectActions = {
  loadProjects: () => Promise<void>
  createProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateProject: (id: string, updates: Partial<Project>) => Promise<void>
  deleteProject: (id: string) => Promise<void>
  setCurrentProject: (project: Project | null) => void
  updateProjectConfig: (id: string, config: Partial<ProjectConfig>) => Promise<void>
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

// 提示词模板状态管理类型
export type PromptTemplateState = {
  templates: PromptTemplate[]
  isLoading: boolean
  error: string | null
}

export type PromptTemplateActions = {
  loadTemplates: () => Promise<void>
  createTemplate: (template: Omit<PromptTemplate, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateTemplate: (id: string, updates: Partial<PromptTemplate>) => Promise<void>
  deleteTemplate: (id: string) => Promise<void>
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

// LLM相关类型定义
export type LLMModel = {
  id: string
  name: string
  provider: string
  maxTokens?: number
  supportsVision?: boolean
}

export type LLMRequest = {
  model: string
  messages: Array<{
    role: "user" | "assistant" | "system"
    content: string | Array<{
      type: "text" | "image_url"
      text?: string
      image_url?: {
        url: string
      }
    }>
  }>
  max_tokens?: number
  temperature?: number
  stream?: boolean
}

export type LLMResponse = {
  id: string
  object: string
  created: number
  model: string
  choices: Array<{
    index: number
    message: {
      role: string
      content: string
    }
    finish_reason: string
  }>
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

export type GenerationRequest = {
  imageUrl?: string
  imageBase64?: string
  promptTemplate?: string
  chatHistory: Message[]
  model?: string
}

export type GenerationResponse = {
  id: string
  content: string
  model: string
  timestamp: Date
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

// LLM状态管理类型
export type LLMState = {
  generations: GenerationResponse[]
  currentGeneration: GenerationResponse | null
  isGenerating: boolean
  error: string | null
  models: LLMModel[]
  selectedModel: string
}

export type LLMActions = {
  generateCode: (request: GenerationRequest) => Promise<GenerationResponse>
  generateCodeStream: (request: GenerationRequest, onChunk?: (chunk: string) => void) => Promise<string>
  cancelGeneration: () => void
  setCurrentGeneration: (generation: GenerationResponse | null) => void
  clearGenerations: () => void
  loadModels: () => Promise<void>
  setSelectedModel: (model: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}
