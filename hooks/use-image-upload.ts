import { useState, useCallback, useEffect } from "react"
import { UploadedImage, UploadError } from "@/types"
import {
  validateImageFile,
  saveImageToLocalStorage,
  removeImageFromLocalStorage,
  getSavedImages
} from "@/lib/image-utils"
import { fileToBase64, compressBase64Image, getBase64Size } from "@/lib/image-base64"

export function useImageUpload() {
  const [uploadedImage, setUploadedImage] = useState<UploadedImage | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<UploadError | null>(null)

  // 从localStorage恢复最后上传的图片状态
  useEffect(() => {
    try {
      const savedImages = getSavedImages()
      if (savedImages.length > 0) {
        // 获取最后一张图片
        const lastImage = savedImages[savedImages.length - 1] as UploadedImage
        setUploadedImage(lastImage)
      }
    } catch (error) {
      console.error('恢复图片状态失败:', error)
    }
  }, [])

  const uploadImage = useCallback(async (file: File) => {
    setIsUploading(true)
    setError(null)

    try {
      // 验证文件
      const validationError = validateImageFile(file)
      if (validationError) {
        setError(validationError)
        return false
      }

      // 创建FormData上传文件
      const formData = new FormData()
      formData.append('file', file)

      // 调用上传API
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        setError({
          type: "upload",
          message: errorData.error || "上传失败，请重试"
        })
        return false
      }

      const uploadResult = await response.json()

      // 清理之前的图片
      if (uploadedImage) {
        removeImageFromLocalStorage(uploadedImage.id)
      }

      // 生成base64编码
      let base64Data: string | undefined
      try {
        base64Data = await fileToBase64(file)

        // 如果图片过大，进行压缩
        const base64Size = getBase64Size(base64Data)
        if (base64Size > 5 * 1024 * 1024) { // 5MB
          base64Data = await compressBase64Image(base64Data, 1024, 1024, 0.8)
        }
      } catch (error) {
        console.error('Base64生成失败:', error)
        // base64生成失败不影响上传流程，只是无法发送给LLM
      }

      // 创建图片对象
      const image: UploadedImage = {
        id: uploadResult.id,
        url: uploadResult.url,
        fileName: uploadResult.fileName,
        originalName: uploadResult.originalName,
        size: uploadResult.size,
        type: uploadResult.type,
        uploadedAt: uploadResult.uploadedAt,
        base64: base64Data
      }

      // 保存到本地存储
      saveImageToLocalStorage(image)

      setUploadedImage(image)
      return true
    } catch (err) {
      console.error('Upload error:', err)
      setError({
        type: "upload",
        message: "上传失败，请重试"
      })
      return false
    } finally {
      setIsUploading(false)
    }
  }, [uploadedImage])

  const removeImage = useCallback(() => {
    if (uploadedImage) {
      removeImageFromLocalStorage(uploadedImage.id)
      setUploadedImage(null)
    }
    setError(null)
  }, [uploadedImage])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    uploadedImage,
    isUploading,
    error,
    uploadImage,
    removeImage,
    clearError
  }
}
