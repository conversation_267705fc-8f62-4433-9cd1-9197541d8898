import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'
import { PromptTemplate } from '@/types'

const TEMPLATES_FILE = path.join(process.cwd(), 'data', 'prompt-templates.json')

// 确保data目录存在
async function ensureDataDirectory() {
  const dataDir = path.join(process.cwd(), 'data')
  try {
    await fs.access(dataDir)
  } catch {
    await fs.mkdir(dataDir, { recursive: true })
  }
}

// 读取模板列表
async function readTemplates(): Promise<PromptTemplate[]> {
  try {
    await ensureDataDirectory()
    const data = await fs.readFile(TEMPLATES_FILE, 'utf-8')
    const templates = JSON.parse(data)
    // 确保日期字段正确解析
    return templates.map((template: any) => ({
      ...template,
      createdAt: new Date(template.createdAt),
      updatedAt: new Date(template.updatedAt)
    }))
  } catch (error) {
    // 如果文件不存在，返回默认模板
    const defaultTemplates: PromptTemplate[] = [
      {
        id: 'default-template',
        name: '默认模板',
        description: '适用于一般设计图转代码的默认提示词模板',
        content: '请分析这张设计图，并生成对应的React组件代码。要求：\n\n1. 使用现代React函数组件和Hooks\n2. 使用Tailwind CSS进行样式设计\n3. 确保组件具有良好的响应式设计\n4. 代码结构清晰，易于维护\n5. 包含必要的TypeScript类型定义\n\n请提供完整的组件代码和使用说明。',
        isDefault: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]
    await writeTemplates(defaultTemplates)
    return defaultTemplates
  }
}

// 写入模板列表
async function writeTemplates(templates: PromptTemplate[]): Promise<void> {
  await ensureDataDirectory()
  await fs.writeFile(TEMPLATES_FILE, JSON.stringify(templates, null, 2))
}

// GET - 获取所有模板
export async function GET() {
  try {
    const templates = await readTemplates()
    return NextResponse.json(templates)
  } catch (error) {
    console.error('Error reading templates:', error)
    return NextResponse.json(
      { error: '读取模板列表失败' },
      { status: 500 }
    )
  }
}

// POST - 创建新模板
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, content } = body

    if (!name || !content) {
      return NextResponse.json(
        { error: '模板名称和内容不能为空' },
        { status: 400 }
      )
    }

    const templates = await readTemplates()

    // 检查模板名称是否已存在
    if (templates.some(t => t.name === name)) {
      return NextResponse.json(
        { error: '模板名称已存在' },
        { status: 400 }
      )
    }

    const newTemplate: PromptTemplate = {
      id: `template-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name,
      description: description || '',
      content,
      isDefault: false,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    templates.push(newTemplate)
    await writeTemplates(templates)

    return NextResponse.json(newTemplate, { status: 201 })
  } catch (error) {
    console.error('Error creating template:', error)
    return NextResponse.json(
      { error: '创建模板失败' },
      { status: 500 }
    )
  }
}
