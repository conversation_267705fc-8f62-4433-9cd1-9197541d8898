import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'
import { PromptTemplate } from '@/types'

const TEMPLATES_FILE = path.join(process.cwd(), 'data', 'prompt-templates.json')

// 读取模板列表
async function readTemplates(): Promise<PromptTemplate[]> {
  try {
    const data = await fs.readFile(TEMPLATES_FILE, 'utf-8')
    const templates = JSON.parse(data)
    return templates.map((template: any) => ({
      ...template,
      createdAt: new Date(template.createdAt),
      updatedAt: new Date(template.updatedAt)
    }))
  } catch (error) {
    return []
  }
}

// 写入模板列表
async function writeTemplates(templates: PromptTemplate[]): Promise<void> {
  await fs.writeFile(TEMPLATES_FILE, JSON.stringify(templates, null, 2))
}

// GET - 获取单个模板
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const templates = await readTemplates()
    const template = templates.find(t => t.id === id)
    
    if (!template) {
      return NextResponse.json(
        { error: '模板不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(template)
  } catch (error) {
    console.error('Error reading template:', error)
    return NextResponse.json(
      { error: '读取模板失败' },
      { status: 500 }
    )
  }
}

// PUT - 更新模板
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const templates = await readTemplates()
    const templateIndex = templates.findIndex(t => t.id === id)
    
    if (templateIndex === -1) {
      return NextResponse.json(
        { error: '模板不存在' },
        { status: 404 }
      )
    }

    // 检查名称冲突（如果更新了名称）
    if (body.name && body.name !== templates[templateIndex].name) {
      if (templates.some(t => t.name === body.name && t.id !== id)) {
        return NextResponse.json(
          { error: '模板名称已存在' },
          { status: 400 }
        )
      }
    }

    // 防止修改默认模板的isDefault属性
    const isDefault = templates[templateIndex].isDefault
    if (isDefault && body.isDefault === false) {
      return NextResponse.json(
        { error: '不能取消默认模板的默认状态' },
        { status: 400 }
      )
    }

    // 更新模板
    const updatedTemplate: PromptTemplate = {
      ...templates[templateIndex],
      ...body,
      id: id, // 确保ID不被修改
      isDefault, // 保持原有的默认状态
      updatedAt: new Date()
    }

    templates[templateIndex] = updatedTemplate
    await writeTemplates(templates)

    return NextResponse.json(updatedTemplate)
  } catch (error) {
    console.error('Error updating template:', error)
    return NextResponse.json(
      { error: '更新模板失败' },
      { status: 500 }
    )
  }
}

// DELETE - 删除模板
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const templates = await readTemplates()
    const templateIndex = templates.findIndex(t => t.id === id)
    
    if (templateIndex === -1) {
      return NextResponse.json(
        { error: '模板不存在' },
        { status: 404 }
      )
    }

    // 防止删除默认模板
    if (templates[templateIndex].isDefault) {
      return NextResponse.json(
        { error: '不能删除默认模板' },
        { status: 400 }
      )
    }

    templates.splice(templateIndex, 1)
    await writeTemplates(templates)

    return NextResponse.json({ message: '模板删除成功' })
  } catch (error) {
    console.error('Error deleting template:', error)
    return NextResponse.json(
      { error: '删除模板失败' },
      { status: 500 }
    )
  }
}
