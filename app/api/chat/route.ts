import { NextRequest, NextResponse } from 'next/server'
import { callLLM, callLLMStream } from '@/lib/llm-service'
import { GenerationRequest } from '@/types'
import { formatErrorMessage, getErrorCategory, getErrorSuggestion } from '@/lib/error-handling'

export async function POST(request: NextRequest) {
  try {
    const body: GenerationRequest = await request.json()

    // 验证请求数据
    if (!body.chatHistory || !Array.isArray(body.chatHistory)) {
      return NextResponse.json(
        { error: '聊天历史数据无效' },
        { status: 400 }
      )
    }

    // 检查是否需要流式响应
    const isStream = request.headers.get('accept') === 'text/stream'

    if (isStream) {
      // 流式响应
      const encoder = new TextEncoder()
      const stream = new ReadableStream({
        async start(controller) {
          try {
            for await (const chunk of callLLMStream(body)) {
              const data = `data: ${JSON.stringify({ content: chunk })}\n\n`
              controller.enqueue(encoder.encode(data))
            }
            
            // 发送结束信号
            controller.enqueue(encoder.encode('data: [DONE]\n\n'))
            controller.close()
          } catch (error) {
            const errorData = `data: ${JSON.stringify({ 
              error: error instanceof Error ? error.message : '生成失败' 
            })}\n\n`
            controller.enqueue(encoder.encode(errorData))
            controller.close()
          }
        },
      })

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      })
    } else {
      // 普通响应
      const response = await callLLM(body)
      return NextResponse.json(response)
    }
  } catch (error) {
    console.error('聊天API错误:', error)

    const errorMessage = formatErrorMessage(error)
    const errorCategory = getErrorCategory(error)
    const suggestion = getErrorSuggestion(error)

    // 根据错误类型返回适当的HTTP状态码
    let status = 500
    if (errorCategory === 'auth') {
      status = 401
    } else if (errorCategory === 'rate_limit') {
      status = 429
    } else if (errorCategory === 'client') {
      status = 400
    } else if (errorCategory === 'timeout') {
      status = 408
    }

    return NextResponse.json(
      {
        error: errorMessage,
        category: errorCategory,
        suggestion,
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status }
    )
  }
}

// 获取支持的模型列表
export async function GET() {
  try {
    const { SUPPORTED_MODELS } = await import('@/lib/llm-service')
    return NextResponse.json({ models: SUPPORTED_MODELS })
  } catch (error) {
    console.error('获取模型列表失败:', error)
    return NextResponse.json(
      { error: '获取模型列表失败' },
      { status: 500 }
    )
  }
}
