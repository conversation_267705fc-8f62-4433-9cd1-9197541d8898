import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'
import { Project, ProjectConfig } from '@/types'

const PROJECTS_FILE = path.join(process.cwd(), 'data', 'projects.json')

// 确保data目录存在
async function ensureDataDirectory() {
  const dataDir = path.join(process.cwd(), 'data')
  try {
    await fs.access(dataDir)
  } catch {
    await fs.mkdir(dataDir, { recursive: true })
  }
}

// 读取项目列表
async function readProjects(): Promise<Project[]> {
  try {
    await ensureDataDirectory()
    const data = await fs.readFile(PROJECTS_FILE, 'utf-8')
    const projects = JSON.parse(data)
    // 确保日期字段正确解析
    return projects.map((project: any) => ({
      ...project,
      createdAt: new Date(project.createdAt),
      updatedAt: new Date(project.updatedAt)
    }))
  } catch (error) {
    // 如果文件不存在，返回默认项目
    const defaultProject: Project = {
      id: 'default-project',
      name: '默认项目',
      description: '这是一个默认项目，您可以在此基础上开始您的设计转代码工作',
      createdAt: new Date(),
      updatedAt: new Date(),
      config: {
        promptTemplateId: 'default-template'
      }
    }
    await writeProjects([defaultProject])
    return [defaultProject]
  }
}

// 写入项目列表
async function writeProjects(projects: Project[]): Promise<void> {
  await ensureDataDirectory()
  await fs.writeFile(PROJECTS_FILE, JSON.stringify(projects, null, 2))
}

// GET - 获取所有项目
export async function GET() {
  try {
    const projects = await readProjects()
    return NextResponse.json(projects)
  } catch (error) {
    console.error('Error reading projects:', error)
    return NextResponse.json(
      { error: '读取项目列表失败' },
      { status: 500 }
    )
  }
}

// POST - 创建新项目
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, config } = body

    if (!name || !config) {
      return NextResponse.json(
        { error: '项目名称和配置不能为空' },
        { status: 400 }
      )
    }

    const projects = await readProjects()
    
    // 检查项目名称是否已存在
    if (projects.some(p => p.name === name)) {
      return NextResponse.json(
        { error: '项目名称已存在' },
        { status: 400 }
      )
    }

    const newProject: Project = {
      id: `project-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name,
      description: description || '',
      createdAt: new Date(),
      updatedAt: new Date(),
      config: {
        promptTemplateId: config.promptTemplateId || ''
      }
    }

    projects.push(newProject)
    await writeProjects(projects)

    return NextResponse.json(newProject, { status: 201 })
  } catch (error) {
    console.error('Error creating project:', error)
    return NextResponse.json(
      { error: '创建项目失败' },
      { status: 500 }
    )
  }
}
