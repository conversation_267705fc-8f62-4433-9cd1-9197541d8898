import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'
import { Project, ProjectConfig } from '@/types'

const PROJECTS_FILE = path.join(process.cwd(), 'data', 'projects.json')

// 读取项目列表
async function readProjects(): Promise<Project[]> {
  try {
    const data = await fs.readFile(PROJECTS_FILE, 'utf-8')
    const projects = JSON.parse(data)
    return projects.map((project: any) => ({
      ...project,
      createdAt: new Date(project.createdAt),
      updatedAt: new Date(project.updatedAt)
    }))
  } catch (error) {
    return []
  }
}

// 写入项目列表
async function writeProjects(projects: Project[]): Promise<void> {
  await fs.writeFile(PROJECTS_FILE, JSON.stringify(projects, null, 2))
}

// GET - 获取项目配置
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const projects = await readProjects()
    const project = projects.find(p => p.id === id)
    
    if (!project) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(project.config)
  } catch (error) {
    console.error('Error reading project config:', error)
    return NextResponse.json(
      { error: '读取项目配置失败' },
      { status: 500 }
    )
  }
}

// PUT - 更新项目配置
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const projects = await readProjects()
    const projectIndex = projects.findIndex(p => p.id === id)
    
    if (projectIndex === -1) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    // 验证配置数据（简化版）
    const config: Partial<ProjectConfig> = {}

    if (body.promptTemplateId !== undefined) config.promptTemplateId = body.promptTemplateId

    // 更新项目配置
    projects[projectIndex] = {
      ...projects[projectIndex],
      config: {
        ...projects[projectIndex].config,
        ...config
      },
      updatedAt: new Date()
    }

    await writeProjects(projects)

    return NextResponse.json(projects[projectIndex].config)
  } catch (error) {
    console.error('Error updating project config:', error)
    return NextResponse.json(
      { error: '更新项目配置失败' },
      { status: 500 }
    )
  }
}
