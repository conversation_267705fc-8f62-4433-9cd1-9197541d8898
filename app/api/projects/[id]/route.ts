import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'
import { Project } from '@/types'

const PROJECTS_FILE = path.join(process.cwd(), 'data', 'projects.json')

// 读取项目列表
async function readProjects(): Promise<Project[]> {
  try {
    const data = await fs.readFile(PROJECTS_FILE, 'utf-8')
    const projects = JSON.parse(data)
    return projects.map((project: any) => ({
      ...project,
      createdAt: new Date(project.createdAt),
      updatedAt: new Date(project.updatedAt)
    }))
  } catch (error) {
    return []
  }
}

// 写入项目列表
async function writeProjects(projects: Project[]): Promise<void> {
  await fs.writeFile(PROJECTS_FILE, JSON.stringify(projects, null, 2))
}

// GET - 获取单个项目
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const projects = await readProjects()
    const project = projects.find(p => p.id === id)
    
    if (!project) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(project)
  } catch (error) {
    console.error('Error reading project:', error)
    return NextResponse.json(
      { error: '读取项目失败' },
      { status: 500 }
    )
  }
}

// PUT - 更新项目
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const projects = await readProjects()
    const projectIndex = projects.findIndex(p => p.id === id)
    
    if (projectIndex === -1) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    // 检查名称冲突（如果更新了名称）
    if (body.name && body.name !== projects[projectIndex].name) {
      if (projects.some(p => p.name === body.name && p.id !== id)) {
        return NextResponse.json(
          { error: '项目名称已存在' },
          { status: 400 }
        )
      }
    }

    // 更新项目
    const updatedProject: Project = {
      ...projects[projectIndex],
      ...body,
      id: id, // 确保ID不被修改
      updatedAt: new Date()
    }

    projects[projectIndex] = updatedProject
    await writeProjects(projects)

    return NextResponse.json(updatedProject)
  } catch (error) {
    console.error('Error updating project:', error)
    return NextResponse.json(
      { error: '更新项目失败' },
      { status: 500 }
    )
  }
}

// DELETE - 删除项目
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const projects = await readProjects()
    const projectIndex = projects.findIndex(p => p.id === id)
    
    if (projectIndex === -1) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    // 防止删除默认项目
    if (projects[projectIndex].id === 'default-project') {
      return NextResponse.json(
        { error: '不能删除默认项目' },
        { status: 400 }
      )
    }

    projects.splice(projectIndex, 1)
    await writeProjects(projects)

    return NextResponse.json({ message: '项目删除成功' })
  } catch (error) {
    console.error('Error deleting project:', error)
    return NextResponse.json(
      { error: '删除项目失败' },
      { status: 500 }
    )
  }
}
