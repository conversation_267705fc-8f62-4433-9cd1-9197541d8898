import { NextRequest, NextResponse } from 'next/server'
import { writeFile } from 'fs/promises'
import { join } from 'path'
import { generateFileId, validateImageFile } from '@/lib/image-utils'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { error: '没有找到文件' },
        { status: 400 }
      )
    }

    // 验证文件
    const validationError = validateImageFile(file)
    if (validationError) {
      return NextResponse.json(
        { error: validationError.message },
        { status: 400 }
      )
    }

    // 生成唯一文件名
    const fileId = generateFileId()
    const fileExtension = file.name.split('.').pop()
    const fileName = `${fileId}.${fileExtension}`
    
    // 获取文件内容
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // 保存文件到 public/uploads 目录
    const uploadDir = join(process.cwd(), 'public', 'uploads')
    const filePath = join(uploadDir, fileName)
    
    await writeFile(filePath, buffer)

    // 返回文件信息
    return NextResponse.json({
      id: fileId,
      fileName: fileName,
      originalName: file.name,
      size: buffer.length, // 使用buffer长度作为文件大小
      type: file.type,
      url: `/uploads/${fileName}`,
      uploadedAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: '文件上传失败' },
      { status: 500 }
    )
  }
}
