import { NextRequest, NextResponse } from 'next/server'
import { callLLM, callLLMStream } from '@/lib/llm-service'
import { GenerationRequest } from '@/types'
import { formatErrorMessage, getErrorCategory, getErrorSuggestion } from '@/lib/error-handling'
import { debugLog } from '@/stores/dev-settings-store'

export async function POST(request: NextRequest) {
  const requestId = `api-generate-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
  const startTime = Date.now()

  debugLog.group(`🔥 代码生成API请求 [${requestId}]`, {
    method: request.method,
    url: request.url,
    timestamp: new Date().toISOString()
  })

  try {
    const body: GenerationRequest = await request.json()

    debugLog.log("📋 请求体数据", {
      model: body.model,
      hasImageBase64: !!body.imageBase64,
      hasImageUrl: !!body.imageUrl,
      chatHistoryLength: body.chatHistory?.length || 0,
      promptTemplateLength: body.promptTemplate?.length || 0
    })

    // 验证请求数据
    if (!body.chatHistory || !Array.isArray(body.chatHistory)) {
      return NextResponse.json(
        { error: '聊天历史数据无效' },
        { status: 400 }
      )
    }

    // 检查是否需要流式响应
    const isStream = request.headers.get('accept') === 'text/stream'

    // 为代码生成添加特定的系统提示词
    const codeGenerationPrompt = `你是一个专业的前端开发专家，专门将设计图转换为高质量的React代码。

请根据用户提供的设计图和要求，生成完整的React组件代码。要求：

1. 使用现代React函数组件和Hooks
2. 使用Tailwind CSS进行样式设计
3. 确保代码结构清晰、可读性强
4. 包含必要的TypeScript类型定义
5. 遵循React最佳实践
6. 确保响应式设计
7. 添加适当的注释说明

请直接输出完整的代码，不需要额外的解释文字。`

    // 如果没有提供提示词模板，使用默认的代码生成提示词
    const enhancedRequest: GenerationRequest = {
      ...body,
      promptTemplate: body.promptTemplate || codeGenerationPrompt,
    }

    if (isStream) {
      // 流式响应
      const encoder = new TextEncoder()
      const stream = new ReadableStream({
        async start(controller) {
          try {
            for await (const chunk of callLLMStream(enhancedRequest)) {
              const data = `data: ${JSON.stringify({ content: chunk })}\n\n`
              controller.enqueue(encoder.encode(data))
            }
            
            // 发送结束信号
            controller.enqueue(encoder.encode('data: [DONE]\n\n'))
            controller.close()
          } catch (error) {
            const errorData = `data: ${JSON.stringify({ 
              error: error instanceof Error ? error.message : '代码生成失败' 
            })}\n\n`
            controller.enqueue(encoder.encode(errorData))
            controller.close()
          }
        },
      })

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      })
    } else {
      // 普通响应
      const response = await callLLM(enhancedRequest)
      return NextResponse.json(response)
    }
  } catch (error) {
    console.error('代码生成API错误:', error)

    const errorMessage = formatErrorMessage(error)
    const errorCategory = getErrorCategory(error)
    const suggestion = getErrorSuggestion(error)

    // 根据错误类型返回适当的HTTP状态码
    let status = 500
    if (errorCategory === 'auth') {
      status = 401
    } else if (errorCategory === 'rate_limit') {
      status = 429
    } else if (errorCategory === 'client') {
      status = 400
    } else if (errorCategory === 'timeout') {
      status = 408
    }

    return NextResponse.json(
      {
        error: errorMessage,
        category: errorCategory,
        suggestion,
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status }
    )
  }
}

// 健康检查
export async function GET() {
  try {
    return NextResponse.json({ 
      status: 'ok', 
      service: 'code-generation',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('代码生成服务健康检查失败:', error)
    return NextResponse.json(
      { error: '服务不可用' },
      { status: 503 }
    )
  }
}
