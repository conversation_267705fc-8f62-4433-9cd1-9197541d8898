"use client"

import { useState, useEffect } from "react"
import { Loader2, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

interface LoadingIndicatorProps {
  isLoading: boolean
  message?: string
  progress?: number
  onCancel?: () => void
  showProgress?: boolean
  size?: "sm" | "md" | "lg"
}

export function LoadingIndicator({
  isLoading,
  message = "正在处理...",
  progress,
  onCancel,
  showProgress = false,
  size = "md"
}: LoadingIndicatorProps) {
  const [dots, setDots] = useState("")

  // 动画点点点效果
  useEffect(() => {
    if (!isLoading) return

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === "...") return ""
        return prev + "."
      })
    }, 500)

    return () => clearInterval(interval)
  }, [isLoading])

  if (!isLoading) return null

  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8"
  }

  const containerClasses = {
    sm: "p-2",
    md: "p-4",
    lg: "p-6"
  }

  return (
    <Card className="border-dashed">
      <CardContent className={containerClasses[size]}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Loader2 className={`${sizeClasses[size]} animate-spin text-primary`} />
            <div className="flex flex-col gap-1">
              <span className="text-sm font-medium">
                {message}{dots}
              </span>
              {showProgress && typeof progress === 'number' && (
                <div className="w-48">
                  <Progress value={progress} className="h-2" />
                  <span className="text-xs text-muted-foreground mt-1">
                    {Math.round(progress)}%
                  </span>
                </div>
              )}
            </div>
          </div>
          
          {onCancel && (
            <Button
              variant="outline"
              size="sm"
              onClick={onCancel}
              className="ml-4"
            >
              <X className="h-4 w-4 mr-2" />
              取消
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// 全屏加载遮罩
interface LoadingOverlayProps {
  isLoading: boolean
  message?: string
  onCancel?: () => void
}

export function LoadingOverlay({
  isLoading,
  message = "正在生成代码...",
  onCancel
}: LoadingOverlayProps) {
  if (!isLoading) return null

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <Card className="w-96">
        <CardContent className="p-6">
          <div className="text-center">
            <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">{message}</h3>
            <p className="text-sm text-muted-foreground mb-4">
              AI正在分析您的需求，这可能需要几分钟时间
            </p>
            {onCancel && (
              <Button variant="outline" onClick={onCancel}>
                <X className="h-4 w-4 mr-2" />
                取消生成
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// 内联加载状态
interface InlineLoadingProps {
  message?: string
  size?: "sm" | "md"
}

export function InlineLoading({
  message = "加载中...",
  size = "sm"
}: InlineLoadingProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-5 w-5"
  }

  return (
    <div className="flex items-center gap-2 text-muted-foreground">
      <Loader2 className={`${sizeClasses[size]} animate-spin`} />
      <span className="text-sm">{message}</span>
    </div>
  )
}

// 按钮加载状态
interface LoadingButtonProps {
  isLoading: boolean
  children: React.ReactNode
  loadingText?: string
  onClick?: () => void
  disabled?: boolean
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
}

export function LoadingButton({
  isLoading,
  children,
  loadingText = "处理中...",
  onClick,
  disabled,
  variant = "default",
  size = "default",
  className
}: LoadingButtonProps) {
  return (
    <Button
      variant={variant}
      size={size}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={className}
    >
      {isLoading ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          {loadingText}
        </>
      ) : (
        children
      )}
    </Button>
  )
}
