"use client"

import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable"
import { SectionA } from "@/components/sections/section-a"
import { SectionB } from "@/components/sections/section-b"
import { SectionC } from "@/components/sections/section-c"

export function MainLayout() {
  return (
    <div className="flex-1 overflow-hidden">
      <ResizablePanelGroup direction="horizontal" className="h-full">
        <ResizablePanel defaultSize={40} minSize={40} className="bg-background p-4">
          <SectionA />
        </ResizablePanel>

        <ResizableHandle withHandle />

        <ResizablePanel defaultSize={10} minSize={10} className="bg-background p-4">
          <SectionB />
        </ResizablePanel>

        <ResizableHandle withHandle />

        <ResizablePanel defaultSize={50} minSize={50} className="bg-background p-4">
          <SectionC />
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  )
}
