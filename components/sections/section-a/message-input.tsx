import { useState, useRef, KeyboardEvent } from "react"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Send } from "lucide-react"
import { useChatStore } from "@/stores/chat-store"

interface MessageInputProps {
  disabled?: boolean
}

export function MessageInput({ disabled = false }: MessageInputProps) {
  const [input, setInput] = useState("")
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const { addMessage, isLoading } = useChatStore()

  const handleSend = () => {
    const trimmedInput = input.trim()
    if (!trimmedInput || disabled || isLoading) return

    // 添加用户消息
    addMessage(trimmedInput, "user")
    
    // 清空输入框
    setInput("")
    
    // 重置textarea高度
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
    }

    // TODO: 这里将来会调用LLM API获取回复
    // 现在先模拟一个简单的回复
    setTimeout(() => {
      const responses = [
        "我收到了你的消息。请上传一张图片，我可以帮你分析并生成相应的代码。",
        "好的，我明白了。你可以描述一下你想要实现的功能吗？",
        "这是一个很好的想法！让我帮你分析一下如何实现。",
        "我会根据你的需求来生成相应的组件代码。",
        "请告诉我更多细节，这样我可以提供更准确的帮助。"
      ]
      const randomResponse = responses[Math.floor(Math.random() * responses.length)]
      addMessage(randomResponse, "assistant")
    }, 800 + Math.random() * 1200) // 随机延迟，模拟真实的API响应时间
  }

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const handleInputChange = (value: string) => {
    setInput(value)
    
    // 自动调整textarea高度
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }

  return (
    <div className="border-t p-3 bg-background">
      <div className="flex gap-2 items-end">
        <div className="flex-1">
          <Textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="输入你的消息... (Enter发送，Shift+Enter换行)"
            disabled={disabled || isLoading}
            className="min-h-[40px] max-h-[120px] resize-none"
            rows={1}
          />
        </div>
        <Button
          size="sm"
          onClick={handleSend}
          disabled={!input.trim() || disabled || isLoading}
          className="h-10 px-3"
        >
          {isLoading ? (
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          ) : (
            <Send className="w-4 h-4" />
          )}
        </Button>
      </div>
      
      <div className="flex justify-between items-center mt-1">
        {input.length > 0 && (
          <div className="text-xs text-muted-foreground">
            {input.length} 字符
          </div>
        )}
        <div className="text-xs text-muted-foreground">
          Enter 发送 • Shift+Enter 换行
        </div>
      </div>
    </div>
  )
}
