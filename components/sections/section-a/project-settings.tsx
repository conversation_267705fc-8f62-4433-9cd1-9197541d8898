"use client"

import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Settings, Plus, FolderOpen } from "lucide-react"
import { useProjectStore } from "@/stores/project-store"
import { usePromptTemplateStore } from "@/stores/prompt-template-store"
import { ProjectManagementDialog } from "./project-management-dialog"
import { PromptTemplateDialog } from "./prompt-template-dialog"

export function ProjectSettings() {
  const [showProjectDialog, setShowProjectDialog] = useState(false)
  const [showTemplateDialog, setShowTemplateDialog] = useState(false)

  const {
    projects,
    currentProject,
    isLoading,
    error,
    loadProjects,
    setCurrentProject
  } = useProjectStore()

  const { loadTemplates } = usePromptTemplateStore()

  // 初始化加载数据
  useEffect(() => {
    loadProjects()
    loadTemplates()
  }, [loadProjects, loadTemplates])

  const handleProjectChange = (projectId: string) => {
    const project = projects.find(p => p.id === projectId)
    if (project) {
      setCurrentProject(project)
    }
  }

  return (
    <>
      <div className="flex flex-col gap-3 mb-4">
        <div className="flex gap-2 items-center">
          <div className="flex-1">
            <Select
              value={currentProject?.id || ""}
              onValueChange={handleProjectChange}
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={isLoading ? "加载中..." : "选择项目"} />
              </SelectTrigger>
              <SelectContent>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowProjectDialog(true)}
          >
            <FolderOpen className="h-4 w-4" />
            项目管理
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowTemplateDialog(true)}
          >
            <Settings className="h-4 w-4" />
            提示词设置
          </Button>
        </div>

        {error && (
          <div className="text-sm text-destructive bg-destructive/10 px-3 py-2 rounded-md">
            {error}
          </div>
        )}

        {currentProject && (
          <div className="text-xs text-muted-foreground">
            当前项目：{currentProject.name}
            {currentProject.description && ` - ${currentProject.description}`}
          </div>
        )}
      </div>

      <ProjectManagementDialog
        open={showProjectDialog}
        onOpenChange={setShowProjectDialog}
      />

      <PromptTemplateDialog
        open={showTemplateDialog}
        onOpenChange={setShowTemplateDialog}
      />
    </>
  )
}
