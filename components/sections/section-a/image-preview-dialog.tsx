"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { X, ZoomIn, ZoomOut, RotateCw } from "lucide-react"
import { UploadedImage } from "@/types"

interface ImagePreviewDialogProps {
  image: UploadedImage | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ImagePreviewDialog({ image, open, onOpenChange }: ImagePreviewDialogProps) {
  const [scale, setScale] = useState(1)
  const [rotation, setRotation] = useState(0)

  // 重置缩放和旋转状态
  useEffect(() => {
    if (open) {
      setScale(1)
      setRotation(0)
    }
  }, [open])

  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.25, 3))
  }

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.25, 0.5))
  }

  const handleRotate = () => {
    setRotation((prev) => (prev + 90) % 360)
  }

  if (!image) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl w-[90vw] max-h-[90vh] flex flex-col">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-lg truncate max-w-[80%]">
            {image.originalName}
          </DialogTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={handleZoomIn}
              title="放大"
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={handleZoomOut}
              title="缩小"
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={handleRotate}
              title="旋转"
            >
              <RotateCw className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        <div className="flex-1 overflow-auto flex items-center justify-center bg-black/5 rounded-md p-4">
          <div
            className="relative transition-all duration-200 ease-in-out"
            style={{
              transform: `scale(${scale}) rotate(${rotation}deg)`,
              maxHeight: "100%",
              maxWidth: "100%",
            }}
          >
            <img
              src={image.url}
              alt={image.originalName}
              className="max-h-[70vh] object-contain"
            />
          </div>
        </div>
        <div className="text-xs text-muted-foreground mt-2">
          提示: 使用按钮可以放大、缩小和旋转图片
        </div>
      </DialogContent>
    </Dialog>
  )
}
