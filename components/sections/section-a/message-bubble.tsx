import { Message } from "@/types"
import { formatTimestamp } from "@/stores/chat-store"
import { cn } from "@/lib/utils"
import { CheckCircle, Clock, XCircle } from "lucide-react"

interface MessageBubbleProps {
  message: Message
}

export function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.role === "user"
  
  const getStatusIcon = () => {
    switch (message.status) {
      case "pending":
        return <Clock className="w-3 h-3 text-muted-foreground animate-pulse" />
      case "sent":
        return <CheckCircle className="w-3 h-3 text-green-500" />
      case "failed":
        return <XCircle className="w-3 h-3 text-red-500" />
      default:
        return null
    }
  }

  return (
    <div className={cn("flex gap-3 mb-4", isUser ? "justify-end" : "")}>
      {!isUser && (
        <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground text-xs font-medium flex-shrink-0">
          AI
        </div>
      )}

      <div className={cn("flex flex-col min-w-0", isUser ? "items-end" : "items-start")}>
        <div
          className={cn(
            "p-3 rounded-lg break-words shadow-sm",
            // 使用更合理的最大宽度计算，考虑头像和间距
            "max-w-[calc(100vw-120px)] sm:max-w-[calc(100vw-200px)] md:max-w-[400px] lg:max-w-[500px]",
            isUser
              ? "bg-primary text-primary-foreground"
              : "bg-muted text-foreground border"
          )}
        >
          <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
        </div>

        <div className={cn("flex items-center gap-1 mt-1", isUser ? "flex-row-reverse" : "")}>
          <span className="text-xs text-muted-foreground">
            {formatTimestamp(message.timestamp)}
          </span>
          {isUser && getStatusIcon()}
        </div>
      </div>

      {isUser && (
        <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center text-secondary-foreground text-xs font-medium flex-shrink-0">
          你
        </div>
      )}
    </div>
  )
}
