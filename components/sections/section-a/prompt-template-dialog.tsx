"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Plus, Edit, Trash2, Co<PERSON>, Save, X, Star } from "lucide-react"
import { usePromptTemplateStore } from "@/stores/prompt-template-store"
import { useProjectStore } from "@/stores/project-store"
import { PromptTemplate } from "@/types"

interface PromptTemplateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function PromptTemplateDialog({ open, onOpenChange }: PromptTemplateDialogProps) {
  const [activeTab, setActiveTab] = useState("list")
  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    content: ""
  })

  const { 
    templates, 
    isLoading, 
    createTemplate, 
    updateTemplate, 
    deleteTemplate 
  } = usePromptTemplateStore()
  
  const { currentProject, updateProjectConfig } = useProjectStore()

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      content: ""
    })
    setEditingTemplate(null)
    setIsCreating(false)
  }

  const handleCreate = () => {
    setIsCreating(true)
    setActiveTab("form")
    resetForm()
  }

  const handleEdit = (template: PromptTemplate) => {
    setEditingTemplate(template)
    setFormData({
      name: template.name,
      description: template.description || "",
      content: template.content
    })
    setActiveTab("form")
    setIsCreating(false)
  }

  const handleCopy = (template: PromptTemplate) => {
    setIsCreating(true)
    setFormData({
      name: `${template.name} - 副本`,
      description: template.description || "",
      content: template.content
    })
    setActiveTab("form")
    setEditingTemplate(null)
  }

  const handleSave = async () => {
    if (!formData.name.trim() || !formData.content.trim()) return

    try {
      if (isCreating) {
        await createTemplate({
          name: formData.name,
          description: formData.description,
          content: formData.content
        })
      } else if (editingTemplate) {
        await updateTemplate(editingTemplate.id, {
          name: formData.name,
          description: formData.description,
          content: formData.content
        })
      }
      
      resetForm()
      setActiveTab("list")
    } catch (error) {
      console.error("Error saving template:", error)
    }
  }

  const handleDelete = async (templateId: string) => {
    try {
      await deleteTemplate(templateId)
    } catch (error) {
      console.error("Error deleting template:", error)
    }
  }

  const handleApplyToProject = async (templateId: string) => {
    if (!currentProject) return
    
    try {
      await updateProjectConfig(currentProject.id, {
        promptTemplateId: templateId
      })
    } catch (error) {
      console.error("Error applying template to project:", error)
    }
  }

  const handleCancel = () => {
    resetForm()
    setActiveTab("list")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>提示词模板管理</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="list">模板列表</TabsTrigger>
            <TabsTrigger value="form">
              {isCreating ? "创建模板" : editingTemplate ? "编辑模板" : "模板表单"}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">所有模板</h3>
              <Button onClick={handleCreate}>
                <Plus className="h-4 w-4" />
                创建模板
              </Button>
            </div>

            <div className="space-y-3">
              {templates.map((template) => (
                <Card key={template.id}>
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <CardTitle className="text-base">{template.name}</CardTitle>
                          {template.isDefault && (
                            <Badge variant="secondary">
                              <Star className="h-3 w-3 mr-1" />
                              默认
                            </Badge>
                          )}
                          {currentProject?.config.promptTemplateId === template.id && (
                            <Badge variant="default">当前使用</Badge>
                          )}
                        </div>
                        {template.description && (
                          <CardDescription className="mt-1">
                            {template.description}
                          </CardDescription>
                        )}
                      </div>
                      <div className="flex gap-2">
                        {currentProject && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleApplyToProject(template.id)}
                            disabled={currentProject.config.promptTemplateId === template.id}
                          >
                            应用
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCopy(template)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(template)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        {!template.isDefault && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>确认删除</AlertDialogTitle>
                                <AlertDialogDescription>
                                  确定要删除模板 "{template.name}" 吗？此操作无法撤销。
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>取消</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDelete(template.id)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  删除
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  {/* <CardContent className="pt-0">
                    <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-md">
                      <div className="line-clamp-3">
                        {template.content}
                      </div>
                    </div>
                    <div className="flex justify-between items-center mt-3 text-xs text-muted-foreground">
                      <span>创建于 {new Date(template.createdAt).toLocaleDateString()}</span>
                      {template.updatedAt !== template.createdAt && (
                        <span>更新于 {new Date(template.updatedAt).toLocaleDateString()}</span>
                      )}
                    </div>
                  </CardContent> */}
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="form" className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">模板名称 *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="输入模板名称"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">模板描述</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="输入模板描述（可选）"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">模板内容 *</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="输入提示词模板内容"
                  rows={12}
                  className="font-mono text-sm"
                />
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={handleCancel}>
                <X className="h-4 w-4" />
                取消
              </Button>
              <Button 
                onClick={handleSave} 
                disabled={!formData.name.trim() || !formData.content.trim() || isLoading}
              >
                <Save className="h-4 w-4" />
                {isCreating ? "创建" : "保存"}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
