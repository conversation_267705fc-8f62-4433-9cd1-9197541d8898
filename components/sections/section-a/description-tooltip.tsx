"use client"

import { FileText, Info } from "lucide-react"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"

interface DescriptionTooltipProps {
  description: string
  className?: string
}

export function DescriptionTooltip({ description, className = "" }: DescriptionTooltipProps) {
  // 截取描述的前50个字符作为预览
  const previewText = description.length > 50 
    ? `${description.substring(0, 50)}...` 
    : description

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center gap-1">
            <span className="text-xs">功能描述</span>
            <Info className="h-3 w-3 opacity-60" />
          </div>
        </TooltipTrigger>
        <TooltipContent 
          side="bottom" 
          align="start"
          className="max-w-md p-3"
        >
          <div className="space-y-2">
            <div className="text-sm text-muted-foreground whitespace-pre-wrap leading-relaxed">
              {description}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
