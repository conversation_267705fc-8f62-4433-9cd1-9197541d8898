"use client"

import { useState } from "react"
import { ProjectSettings } from "./project-settings"
import { ImagePreview } from "./image-preview"
import { LLMIntegration } from "./llm-integration"
import { ChatWindow } from "./chat-window"
import { FunctionDescriptionInput } from "./function-description-input"
import { DescriptionTooltip } from "./description-tooltip"

type AppStage = 'initial' | 'chat'

export function SectionA() {
  const [currentStage, setCurrentStage] = useState<AppStage>('initial')
  const [functionDescription, setFunctionDescription] = useState('')

  const handleStageChange = (stage: AppStage) => {
    setCurrentStage(stage)
  }

  return (
    <div className="h-full flex flex-col">
      <ProjectSettings />
      <ImagePreview />

      {/* 初始阶段：功能描述输入和生成代码按钮 */}
      {currentStage === 'initial' && (
        <>
          <div className="mb-4">
            <FunctionDescriptionInput
              value={functionDescription}
              onChange={setFunctionDescription}
            />
          </div>
          <LLMIntegration
            functionDescription={functionDescription}
            onStageChange={handleStageChange}
          />
        </>
      )}

      {/* 聊天阶段：描述提示和聊天窗口 */}
      {currentStage === 'chat' && (
        <>
          {functionDescription && (
            <div className="mb-3">
              <DescriptionTooltip description={functionDescription} />
            </div>
          )}
          <ChatWindow />
        </>
      )}
    </div>
  )
}
