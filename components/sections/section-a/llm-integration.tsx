"use client"

import { useState } from "react"
import { Sparkles } from "lucide-react"
import { Button } from "@/components/ui/button"
import { toast } from "sonner"
import { useChatStore } from "@/stores/chat-store"
import { useProjectStore } from "@/stores/project-store"
import { usePromptTemplateStore } from "@/stores/prompt-template-store"
import { useImageUpload } from "@/hooks/use-image-upload"
import { buildGenerationRequest, getCurrentProjectPromptTemplate } from "@/lib/chat-utils"

interface LLMIntegrationProps {
  functionDescription: string
  onStageChange: (stage: 'initial' | 'chat') => void
}

export function LLMIntegration({ functionDescription, onStageChange }: LLMIntegrationProps) {
  const [isGenerating, setIsGenerating] = useState(false)

  // Store hooks
  const { addMessage } = useChatStore()
  const { currentProject } = useProjectStore()
  const { templates } = usePromptTemplateStore()
  const { uploadedImage } = useImageUpload()

  // 检查是否可以生成代码
  const canGenerate = uploadedImage || (functionDescription.trim().length > 0) && !isGenerating

  // 生成代码处理函数
  const handleGenerateCode = async () => {
    if (!canGenerate || !uploadedImage) {
      if (!uploadedImage || !functionDescription.trim()) {
        toast.error("请先上传图片或者填写页面功能描述")
      }
      return
    }

    setIsGenerating(true)

    try {
      // 获取当前项目的提示词模板
      const promptTemplate = await getCurrentProjectPromptTemplate(currentProject, templates)

      // 创建一个包含功能描述的用户消息
      const userMessage = {
        id: `msg-${Date.now()}`,
        role: 'user' as const,
        content: functionDescription,
        timestamp: new Date(),
        status: 'sent' as const,
        messageType: 'text' as const
      }

      // 构建生成请求
      const request = buildGenerationRequest(
        [userMessage],
        uploadedImage,
        currentProject,
        promptTemplate
      )

      // 输出完整的请求数据到控制台
      console.group("🚀 LLM 代码生成请求数据")
      console.log("📋 完整请求对象:", request)
      console.log("🖼️ 图片数据:", {
        hasBase64: !!uploadedImage.base64,
        hasUrl: !!uploadedImage.url,
        fileName: uploadedImage.fileName || uploadedImage.originalName,
        size: uploadedImage.size,
        type: uploadedImage.type
      })
      if (uploadedImage.base64) {
        console.log("📊 Base64 数据大小:", Math.round(uploadedImage.base64.length / 1024), "KB")
        console.log("🔗 Base64 前缀:", uploadedImage.base64.substring(0, 50) + "...")
      }
      console.log("📝 功能描述:", functionDescription)
      console.log("🏗️ 项目信息:", currentProject)
      console.log("📄 提示词模板:", promptTemplate)
      console.log("💬 聊天历史:", request.chatHistory)
      console.groupEnd()

      // 模拟生成过程
      toast.success("正在生成代码，请稍候...")

      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 添加初始消息到聊天
      addMessage("我已经分析了您上传的图片和功能描述，正在为您生成相应的代码。", "assistant", "text")
      addMessage("代码生成完成！您可以继续与我对话来优化和调整代码。", "assistant", "text")

      // 切换到聊天阶段
      onStageChange('chat')

      toast.success("代码生成完成！已切换到聊天模式")
    } catch (error) {
      console.error("代码生成失败:", error)
      const errorMessage = error instanceof Error ? error.message : "代码生成失败"
      toast.error(errorMessage)
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="mb-4">
      <Button
        onClick={handleGenerateCode}
        disabled={!canGenerate}
        className="w-full h-12 text-base font-medium"
        size="lg"
      >
        {isGenerating ? (
          <>
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
            正在生成代码...
          </>
        ) : (
          <>
            <Sparkles className="h-5 w-5 mr-2" />
            生成代码
          </>
        )}
      </Button>

      {/* 提示信息 */}
      {!canGenerate && !isGenerating && (
        <div className="mt-2 text-sm text-muted-foreground text-center">
          {!uploadedImage && !functionDescription.trim() ? (
            "请上传图片并填写功能描述"
          ) : !uploadedImage ? (
            "请先上传图片"
          ) : !functionDescription.trim() ? (
            "请填写功能描述"
          ) : null}
        </div>
      )}
    </div>
  )
}
