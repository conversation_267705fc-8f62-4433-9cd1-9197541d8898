"use client"

import { Sparkles } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import { useChatStore } from "@/stores/chat-store"
import { useProjectStore } from "@/stores/project-store"
import { usePromptTemplateStore } from "@/stores/prompt-template-store"
import { useLLMStore } from "@/stores/llm-store"
import { useDevSettingsStore, debugLog } from "@/stores/dev-settings-store"
import { useImageUpload } from "@/hooks/use-image-upload"
import { buildGenerationRequest, getCurrentProjectPromptTemplate } from "@/lib/chat-utils"

interface LLMIntegrationProps {
  functionDescription: string
  onStageChange: (stage: 'initial' | 'chat') => void
}

export function LLMIntegration({ functionDescription, onStageChange }: LLMIntegrationProps) {
  // Store hooks
  const { addMessage } = useChatStore()
  const { currentProject } = useProjectStore()
  const { templates } = usePromptTemplateStore()
  const { uploadedImage } = useImageUpload()
  const { generateCodeStream, isGenerating, setCurrentGeneration } = useLLMStore()
  const { enableLLMSimulation, simulationDelay } = useDevSettingsStore()

  // 检查是否可以生成代码
  const canGenerate = (uploadedImage && functionDescription.trim().length > 0) && !isGenerating

  // 模拟代码生成（开发模式）
  const simulateCodeGeneration = async (request: any) => {
    debugLog.group("🎭 模拟代码生成", {
      delay: simulationDelay,
      request: request
    })

    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, simulationDelay))

    // 模拟生成的代码内容
    const mockCode = `import React from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function GeneratedComponent() {
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>生成的组件</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            这是基于您的图片和描述生成的React组件。
          </p>
          <p className="mb-4">功能描述: ${functionDescription}</p>
          <Button>示例按钮</Button>
        </CardContent>
      </Card>
    </div>
  )
}

// 样式说明：
// - 使用了Tailwind CSS进行样式设计
// - 采用了Card组件布局
// - 响应式设计，适配不同屏幕尺寸`

    debugLog.success("模拟代码生成完成", { codeLength: mockCode.length })
    debugLog.groupEnd()

    return mockCode
  }

  // 生成代码处理函数
  const handleGenerateCode = async () => {
    if (!canGenerate) {
      if (!uploadedImage) {
        toast.error("请先上传图片")
      } else if (!functionDescription.trim()) {
        toast.error("请填写页面功能描述")
      }
      return
    }

    try {
      // 获取当前项目的提示词模板
      const promptTemplate = await getCurrentProjectPromptTemplate(currentProject, templates)

      // 创建一个包含功能描述的用户消息
      const userMessage = {
        id: `msg-${Date.now()}`,
        role: 'user' as const,
        content: functionDescription,
        timestamp: new Date(),
        status: 'sent' as const,
        messageType: 'text' as const
      }

      // 构建生成请求
      const request = buildGenerationRequest(
        [userMessage],
        uploadedImage,
        currentProject,
        promptTemplate
      )

      // 详细的调试日志
      debugLog.group("🚀 LLM 代码生成请求", {
        mode: enableLLMSimulation ? '模拟模式' : '真实API',
        timestamp: new Date().toISOString()
      })
      debugLog.log("📋 完整请求对象", request)
      debugLog.log("🖼️ 图片数据", {
        hasBase64: !!uploadedImage.base64,
        hasUrl: !!uploadedImage.url,
        fileName: uploadedImage.fileName || uploadedImage.originalName,
        size: uploadedImage.size,
        type: uploadedImage.type
      })
      if (uploadedImage.base64) {
        debugLog.log("📊 Base64 数据大小", `${Math.round(uploadedImage.base64.length / 1024)} KB`)
        debugLog.log("🔗 Base64 前缀", uploadedImage.base64.substring(0, 50) + "...")
      }
      debugLog.log("📝 功能描述", functionDescription)
      debugLog.log("🏗️ 项目信息", currentProject)
      debugLog.log("📄 提示词模板", promptTemplate)
      debugLog.log("💬 聊天历史", request.chatHistory)
      debugLog.groupEnd()

      if (enableLLMSimulation) {
        // 模拟模式
        toast.success("正在模拟生成代码，请稍候...")

        const mockCode = await simulateCodeGeneration(request)

        // 创建模拟的生成结果并更新到LLM store
        const mockGeneration = {
          id: `gen-${Date.now()}`,
          content: mockCode,
          model: 'mock-model',
          timestamp: new Date(),
        }
        setCurrentGeneration(mockGeneration)

        // 添加模拟消息到聊天
        addMessage("我已经分析了您上传的图片和功能描述，正在为您生成相应的代码。", "assistant", "text")
        addMessage("代码生成完成！您可以继续与我对话来优化和调整代码。", "assistant", "text")

        toast.success("模拟代码生成完成！已切换到聊天模式")
      } else {
        // 真实API调用
        toast.success("正在生成代码，请稍候...")

        // 使用流式生成
        await generateCodeStream(request, (chunk) => {
          debugLog.log("📥 收到流式数据块", chunk)
        })

        // 添加消息到聊天
        addMessage("我已经分析了您上传的图片和功能描述，正在为您生成相应的代码。", "assistant", "text")
        addMessage("代码生成完成！您可以继续与我对话来优化和调整代码。", "assistant", "text")

        toast.success("代码生成完成！已切换到聊天模式")
      }

      // 切换到聊天阶段
      onStageChange('chat')

    } catch (error) {
      debugLog.error("代码生成失败", error)
      const errorMessage = error instanceof Error ? error.message : "代码生成失败"
      toast.error(errorMessage)
    }
  }

  return (
    <div className="mb-4">
      <Button
        onClick={handleGenerateCode}
        disabled={!canGenerate}
        className="w-full h-12 text-base font-medium"
        size="lg"
      >
        {isGenerating ? (
          <>
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
            正在生成代码...
          </>
        ) : (
          <>
            <Sparkles className="h-5 w-5 mr-2" />
            生成代码
          </>
        )}
      </Button>

      {/* 提示信息 */}
      {!canGenerate && !isGenerating && (
        <div className="mt-2 text-sm text-muted-foreground text-center">
          {!uploadedImage && !functionDescription.trim() ? (
            "请上传图片并填写功能描述"
          ) : !uploadedImage ? (
            "请先上传图片"
          ) : !functionDescription.trim() ? (
            "请填写功能描述"
          ) : null}
        </div>
      )}
    </div>
  )
}
