import { useEffect, useRef } from "react"
import { useChatStore } from "@/stores/chat-store"
import { <PERSON><PERSON><PERSON>eader } from "./chat-header"
import { MessageBubble } from "./message-bubble"
import { MessageInput } from "./message-input"
import { MessageSquare } from "lucide-react"

interface ChatWindowProps {
  hidden?: boolean
  disabled?: boolean
}

export function ChatWindow({ hidden = false, disabled = false }: ChatWindowProps) {
  const { messages } = useChatStore()
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  // 自动滚动到最新消息
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 空状态显示
  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="text-center text-muted-foreground">
        <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
        <h3 className="text-lg font-medium mb-2">开始对话</h3>
        <p className="text-sm">
          发送消息开始与AI助手的对话，<br />
          我可以帮你分析图片和生成代码。
        </p>
      </div>
    </div>
  )

  // 如果隐藏，不渲染组件
  if (hidden) {
    return null
  }

  return (
    <div className={`flex-1 flex flex-col border rounded-md overflow-hidden bg-background ${disabled ? 'opacity-50 pointer-events-none' : ''}`}>
      <ChatHeader />

      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto scroll-smooth"
      >
        {messages.length === 0 ? (
          <EmptyState />
        ) : (
          <div className="px-3 py-4 space-y-2">
            {messages.map((message) => (
              <MessageBubble key={message.id} message={message} />
            ))}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      <MessageInput disabled={disabled} />
    </div>
  )
}
