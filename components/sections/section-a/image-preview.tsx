"use client"

import { useRef, useState, Drag<PERSON><PERSON>, ChangeEvent } from "react"
import { Upload, X, <PERSON>ader2, <PERSON>ertCircle, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useImageUpload } from "@/hooks/use-image-upload"
import { formatFileSize } from "@/lib/image-utils"
import { ImagePreviewDialog } from "./image-preview-dialog"

export function ImagePreview() {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isDragOver, setIsDragOver] = useState(false)
  const [showPreviewDialog, setShowPreviewDialog] = useState(false)
  const { uploadedImage, isUploading, error, uploadImage, removeImage, clearError } = useImageUpload()

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return

    const file = files[0]
    await uploadImage(file)
  }

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files)
  }

  const handleDragOver = (e: DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  const handleRemoveImage = () => {
    removeImage()
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className="mb-4">
      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error.message}
            <Button
              variant="ghost"
              size="sm"
              className="ml-2 h-auto p-0 text-destructive hover:text-destructive"
              onClick={clearError}
            >
              关闭
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* 上传区域或图片预览 */}
      {uploadedImage ? (
        <div className="relative border rounded-md overflow-hidden bg-background">
          <img
            src={uploadedImage.url}
            alt={uploadedImage.originalName}
            className="w-full h-[200px] object-contain cursor-pointer"
            onClick={() => setShowPreviewDialog(true)}
          />
          <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowPreviewDialog(true)}
              className="gap-2"
            >
              <Eye className="h-4 w-4" />
              查看大图
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleRemoveImage}
              className="gap-2"
            >
              <X className="h-4 w-4" />
              删除图片
            </Button>
          </div>
          <div className="flex space-betweenabsolute bottom-0 left-0 right-0 bg-black/30 text-white p-2 text-xs">
            <p className="truncate">{uploadedImage.originalName}</p>
            <p>{formatFileSize(uploadedImage.size)}</p>
          </div>
        </div>
      ) : (
        <div
          className={`h-[200px] w-full border-1 border-dashed rounded-md flex flex-col items-center justify-center cursor-pointer transition-colors ${
            isDragOver
              ? "border-primary bg-primary/10"
              : "border-muted-foreground/25 bg-muted hover:bg-muted/80"
          } ${isUploading ? "pointer-events-none" : ""}`}
          onClick={handleClick}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {isUploading ? (
            <>
              <Loader2 className="h-10 w-10 text-muted-foreground mb-2 animate-spin" />
              <p className="text-muted-foreground">正在上传...</p>
            </>
          ) : (
            <>
              <Upload className="h-10 w-10 text-muted-foreground mb-2" />
              <div className="text-center">
                <p className="text-muted-foreground">点击上传图片</p>
                <p className="text-xs text-muted-foreground">或拖拽图片到此处</p>
                <p className="text-xs text-muted-foreground mt-1">
                  支持 PNG、JPG、JPEG 格式，最大 10MB
                </p>
              </div>
            </>
          )}
        </div>
      )}

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/png,image/jpeg,image/jpg"
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* 图片预览对话框 */}
      <ImagePreviewDialog
        image={uploadedImage}
        open={showPreviewDialog}
        onOpenChange={setShowPreviewDialog}
      />
    </div>
  )
}
