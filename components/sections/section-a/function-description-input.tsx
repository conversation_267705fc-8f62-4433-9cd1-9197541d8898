"use client"

import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { FileText } from "lucide-react"

interface FunctionDescriptionInputProps {
  value: string
  onChange: (value: string) => void
  disabled?: boolean
  placeholder?: string
}

const pl:string = `请详细描述您想要实现的页面功能和交互效果...

例如：
- 创建一个响应式的产品展示页面
- 包含产品图片轮播、价格显示、购买按钮
- 支持移动端适配
- 添加产品评价和推荐功能
`;

export function FunctionDescriptionInput({
  value,
  onChange,
  disabled = false,
  placeholder = pl
}: FunctionDescriptionInputProps) {
  return (
    <div className="space-y-3">
      <Label htmlFor="function-description" className="flex items-center gap-2 text-sm font-medium">
        <FileText className="h-4 w-4 text-primary" />
        页面功能描述
      </Label>
      
      <div className="relative">
        <Textarea
          id="function-description"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          placeholder={placeholder}
          className="min-h-[200px] max-h-[500px] resize-y text-sm leading-relaxed"
          rows={8}
        />
        
        {/* 字符计数 */}
        <div className="absolute bottom-2 right-2 text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded">
          {value.length} / 2000
        </div>
      </div>
      
      {/* 提示信息 */}
      <div className="text-xs text-muted-foreground space-y-1">
        <p>💡 <strong>提示：</strong>详细的功能描述有助于AI生成更准确的代码</p>
        <p>📝 建议包含：页面布局、交互功能、样式要求、数据校验需求等</p>
      </div>
    </div>
  )
}
