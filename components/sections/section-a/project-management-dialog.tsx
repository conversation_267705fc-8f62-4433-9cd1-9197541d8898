"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Plus, <PERSON>, Trash2, <PERSON><PERSON><PERSON>, Save, <PERSON> } from "lucide-react"
import { useProjectStore } from "@/stores/project-store"
import { usePromptTemplateStore } from "@/stores/prompt-template-store"
import { Project, ProjectConfig } from "@/types"

interface ProjectManagementDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ProjectManagementDialog({ open, onOpenChange }: ProjectManagementDialogProps) {
  const [activeTab, setActiveTab] = useState("list")
  const [editingProject, setEditingProject] = useState<Project | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    config: {
      promptTemplateId: ""
    }
  })

  const { 
    projects, 
    isLoading, 
    createProject, 
    updateProject, 
    deleteProject,
    updateProjectConfig 
  } = useProjectStore()
  
  const { templates } = usePromptTemplateStore()

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      config: {
        promptTemplateId: ""
      }
    })
    setEditingProject(null)
    setIsCreating(false)
  }

  const handleCreate = () => {
    setIsCreating(true)
    setActiveTab("form")
    resetForm()
  }

  const handleEdit = (project: Project) => {
    setEditingProject(project)
    setFormData({
      name: project.name,
      description: project.description || "",
      config: { ...project.config }
    })
    setActiveTab("form")
    setIsCreating(false)
  }

  const handleSave = async () => {
    if (!formData.name.trim()) return

    try {
      if (isCreating) {
        await createProject({
          name: formData.name,
          description: formData.description,
          config: formData.config
        })
      } else if (editingProject) {
        await updateProject(editingProject.id, {
          name: formData.name,
          description: formData.description,
          config: formData.config
        })
      }
      
      resetForm()
      setActiveTab("list")
    } catch (error) {
      console.error("Error saving project:", error)
    }
  }

  const handleDelete = async (projectId: string) => {
    try {
      await deleteProject(projectId)
    } catch (error) {
      console.error("Error deleting project:", error)
    }
  }

  const handleCancel = () => {
    resetForm()
    setActiveTab("list")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>项目管理</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="list">项目列表</TabsTrigger>
            <TabsTrigger value="form">
              {isCreating ? "创建项目" : editingProject ? "编辑项目" : "项目表单"}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">所有项目</h3>
              <Button onClick={handleCreate}>
                <Plus className="h-4 w-4" />
                创建项目
              </Button>
            </div>

            <div className="grid gap-4">
              {projects.map((project) => (
                <Card key={project.id}>
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-base">{project.name}</CardTitle>
                        {project.description && (
                          <CardDescription className="mt-1">
                            {project.description}
                          </CardDescription>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(project)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        {project.id !== "default-project" && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>确认删除</AlertDialogTitle>
                                <AlertDialogDescription>
                                  确定要删除项目 "{project.name}" 吗？此操作无法撤销。
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>取消</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDelete(project.id)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  删除
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
                      <span>创建于 {new Date(project.createdAt).toLocaleDateString()}</span>
                      {project.updatedAt !== project.createdAt && (
                        <>
                          <span>•</span>
                          <span>更新于 {new Date(project.updatedAt).toLocaleDateString()}</span>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="form" className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">项目名称 *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="输入项目名称"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">项目描述</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="输入项目描述（可选）"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="promptTemplate">提示词模板</Label>
                <Select
                  value={formData.config.promptTemplateId}
                  onValueChange={(value) => setFormData(prev => ({
                    ...prev,
                    config: { ...prev.config, promptTemplateId: value }
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择提示词模板（可选）" />
                  </SelectTrigger>
                  <SelectContent>
                    {templates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name}
                        {template.isDefault && <Badge variant="secondary" className="ml-2">默认</Badge>}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={handleCancel}>
                <X className="h-4 w-4" />
                取消
              </Button>
              <Button onClick={handleSave} disabled={!formData.name.trim() || isLoading}>
                <Save className="h-4 w-4" />
                {isCreating ? "创建" : "保存"}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
