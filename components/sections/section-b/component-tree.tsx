"use client"

import { ChevronDown, ChevronRight, File, Folder } from "lucide-react"
import { useState } from "react"
import type { TreeNode } from "@/types"

const demoData: TreeNode[] = [
  {
    id: "1",
    name: "App",
    type: "folder",
    expanded: true,
    children: [
      {
        id: "2",
        name: "Header",
        type: "file",
      },
      {
        id: "3",
        name: "Main",
        type: "folder",
        expanded: true,
        children: [
          {
            id: "4",
            name: "Sidebar",
            type: "file",
          },
          {
            id: "5",
            name: "Content",
            type: "folder",
            children: [
              {
                id: "6",
                name: "ArticleList",
                type: "file",
              },
              {
                id: "7",
                name: "ArticleDetail",
                type: "file",
              },
            ],
          },
        ],
      },
      {
        id: "8",
        name: "Footer",
        type: "file",
      },
    ],
  },
]

type TreeNodeProps = {
  node: TreeNode
  level: number
}

function TreeNodeComponent({ node, level }: TreeNodeProps) {
  const [expanded, setExpanded] = useState(node.expanded || false)
  const hasChildren = node.children && node.children.length > 0

  return (
    <div>
      <div
        className="flex items-center py-1 hover:bg-muted/50 rounded px-1 cursor-pointer"
        style={{ paddingLeft: `${level * 16}px` }}
        onClick={() => hasChildren && setExpanded(!expanded)}
      >
        {hasChildren ? (
          expanded ? (
            <ChevronDown className="h-4 w-4 mr-1 text-muted-foreground" />
          ) : (
            <ChevronRight className="h-4 w-4 mr-1 text-muted-foreground" />
          )
        ) : (
          <span className="w-5"></span>
        )}
        {node.type === "folder" ? (
          <Folder className="h-4 w-4 mr-2 text-blue-500" />
        ) : (
          <File className="h-4 w-4 mr-2 text-gray-500" />
        )}
        <span className="text-sm">{node.name}</span>
      </div>
      {expanded && node.children && (
        <div>
          {node.children.map((child) => (
            <TreeNodeComponent key={child.id} node={child} level={level + 1} />
          ))}
        </div>
      )}
    </div>
  )
}

export function ComponentTree() {
  return (
    <div className="border rounded-md p-2 bg-background overflow-auto h-[calc(100%-3rem)]">
      {demoData.map((node) => (
        <TreeNodeComponent key={node.id} node={node} level={0} />
      ))}
    </div>
  )
}
