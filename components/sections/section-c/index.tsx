import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON> } from "./browser"
import { CodeViewer } from "./code-viewer"

export function SectionC() {
  return (
    <div className="h-full">
      <Tabs defaultValue="preview" className="h-full flex flex-col">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="preview">预览</TabsTrigger>
          <TabsTrigger value="code">代码</TabsTrigger>
        </TabsList>

        <TabsContent value="preview" className="flex-1 mt-2 h-0 overflow-hidden">
          <Browser />
        </TabsContent>

        <TabsContent value="code" className="flex-1 mt-2 h-0 overflow-hidden">
          <CodeViewer />
        </TabsContent>
      </Tabs>
    </div>
  )
}
