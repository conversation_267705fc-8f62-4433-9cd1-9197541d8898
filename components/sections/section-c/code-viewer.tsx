"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, Download, Refresh<PERSON><PERSON>, Setting<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { toast } from "sonner"
import { useLLMStore } from "@/stores/llm-store"
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism'

export function CodeViewer() {
  const { currentGeneration, isGenerating, error } = useLLMStore()
  const [copied, setCopied] = useState(false)

  // 复制代码到剪贴板
  const copyToClipboard = async () => {
    if (!currentGeneration?.content) return

    try {
      await navigator.clipboard.writeText(currentGeneration.content)
      setCopied(true)
      toast.success("代码已复制到剪贴板")
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('复制失败:', error)
      toast.error("复制失败，请手动复制")
    }
  }

  // 下载代码文件
  const downloadCode = () => {
    if (!currentGeneration?.content) return

    const blob = new Blob([currentGeneration.content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `generated-code-${Date.now()}.tsx`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast.success("代码文件已下载")
  }

  // 检测代码语言
  const detectLanguage = (code: string): string => {
    if (code.includes('import React') || code.includes('export default') || code.includes('tsx')) {
      return 'tsx'
    }
    if (code.includes('function') || code.includes('const') || code.includes('let')) {
      return 'javascript'
    }
    if (code.includes('<div') || code.includes('<span')) {
      return 'jsx'
    }
    return 'typescript'
  }

  // 格式化时间戳
  const formatTimestamp = (timestamp: Date): string => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(timestamp))
  }

  if (error) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="text-red-600">生成失败</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button variant="outline" onClick={() => window.location.reload()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              重试
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (isGenerating) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle>正在生成代码...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">AI正在分析您的需求并生成代码，请稍候...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!currentGeneration) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle>代码预览</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">暂无生成的代码</h3>
            <p className="text-muted-foreground mb-4">
              上传图片并发送消息给AI，生成的代码将在这里显示
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const language = detectLanguage(currentGeneration.content)

  return (
    <Card className="h-full flex flex-col overflow-hidden">
      <CardHeader className="pb-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle>生成的代码</CardTitle>
            <Badge variant="secondary">{language.toUpperCase()}</Badge>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={copyToClipboard}
              disabled={!currentGeneration.content}
            >
              <Copy className="h-4 w-4 mr-2" />
              {copied ? "已复制" : "复制"}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={downloadCode}
              disabled={!currentGeneration.content}
            >
              <Download className="h-4 w-4 mr-2" />
              下载
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>模型: {currentGeneration.model}</span>
          <Separator orientation="vertical" className="h-4" />
          <span>生成时间: {formatTimestamp(currentGeneration.timestamp)}</span>
          {currentGeneration.usage && (
            <>
              <Separator orientation="vertical" className="h-4" />
              <span>Token: {currentGeneration.usage.total_tokens}</span>
            </>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 p-0 min-h-0">
        <div className="h-full bg-[#282c34] rounded-b-lg overflow-hidden">
          <ScrollArea className="h-full w-full">
            <div className="p-4">
              <SyntaxHighlighter
                language={language}
                style={oneDark}
                customStyle={{
                  margin: 0,
                  borderRadius: 0,
                  background: 'transparent',
                  padding: 0,
                }}
                codeTagProps={{
                  style: {
                    fontSize: '14px',
                    fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                    background: 'transparent',
                  }
                }}
                wrapLines={true}
                wrapLongLines={true}
              >
                {currentGeneration.content}
              </SyntaxHighlighter>
            </div>
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  )
}
