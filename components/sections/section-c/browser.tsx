import { ArrowLeft, ArrowR<PERSON>, RefreshCw, X, Plus, Search } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export function Browser() {
  return (
    <div className="h-full flex flex-col border rounded-md overflow-hidden">
      {/* Browser Chrome */}
      <div className="bg-muted/50 border-b px-2 py-2">
        {/* Tab Bar */}
        <div className="flex items-center mb-2">
          <div className="flex items-center bg-background rounded-t-md border border-b-0 px-3 py-1.5 text-sm">
            <span className="mr-2 truncate max-w-[120px]">Preview</span>
            <Button variant="ghost" size="icon" className="h-4 w-4 ml-1">
              <X className="h-3 w-3" />
            </Button>
          </div>
          <Button variant="ghost" size="icon" className="h-6 w-6 ml-1">
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {/* Navigation Bar */}
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowRight className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <RefreshCw className="h-4 w-4" />
          </Button>

          {/* Address Bar */}
          <div className="flex-1 flex items-center bg-background rounded-md border px-3 py-1.5">
            <Search className="h-4 w-4 text-muted-foreground mr-2" />
            <span className="text-sm text-muted-foreground">https://example.com/preview</span>
          </div>
        </div>
      </div>

      {/* Browser Content */}
      <div className="flex-1 bg-white p-4 overflow-auto">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-4">Welcome to the Preview</h1>
          <p className="mb-4">
            This is a preview of your component. As you make changes in the editor, they will be reflected here.
          </p>

          <div className="border rounded-md p-4 mb-4">
            <h2 className="text-xl font-semibold mb-2">Sample Component</h2>
            <p>This is a sample component that would be rendered based on your code.</p>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="border rounded-md p-4">
              <h3 className="font-medium mb-2">Feature 1</h3>
              <p className="text-sm text-muted-foreground">Description of feature 1</p>
            </div>
            <div className="border rounded-md p-4">
              <h3 className="font-medium mb-2">Feature 2</h3>
              <p className="text-sm text-muted-foreground">Description of feature 2</p>
            </div>
          </div>

          <div className="bg-muted/20 rounded-md p-4">
            <h3 className="font-medium mb-2">Console Output</h3>
            <div className="font-mono text-sm">
              <p>Component mounted</p>
              <p>Props received: &#123; title: "Welcome" &#125;</p>
              <p>Rendering complete</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
