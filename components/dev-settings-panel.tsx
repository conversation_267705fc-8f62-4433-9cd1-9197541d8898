"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { useDevSettingsStore } from "@/stores/dev-settings-store"

export function DevSettingsPanel() {
  const [isOpen, setIsOpen] = useState(false)
  const {
    isDevelopmentMode,
    enableLLMSimulation,
    enableDetailedLogging,
    showDebugPanel,
    simulationDelay,
    toggleDevelopmentMode,
    toggleLLMSimulation,
    toggleDetailedLogging,
    toggleDebugPanel,
    setSimulationDelay,
    resetSettings,
  } = useDevSettingsStore()

  // 只在开发环境下显示
  if (process.env.NODE_ENV === 'production') {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="fixed bottom-4 right-4 z-50 shadow-lg"
        >
          <Settings className="h-4 w-4 mr-2" />
          开发设置
          {enableLLMSimulation && (
            <Badge variant="secondary" className="ml-2">
              模拟
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            开发者设置
          </DialogTitle>
          <DialogDescription>
            调试和开发辅助功能设置
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 开发模式 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Zap className="h-4 w-4" />
                开发模式
              </CardTitle>
              <CardDescription className="text-xs">
                启用开发者功能和调试工具
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-center justify-between">
                <Label htmlFor="dev-mode" className="text-sm">
                  开发模式
                </Label>
                <Switch
                  id="dev-mode"
                  checked={isDevelopmentMode}
                  onCheckedChange={toggleDevelopmentMode}
                />
              </div>
            </CardContent>
          </Card>

          {/* LLM设置 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Zap className="h-4 w-4" />
                LLM设置
              </CardTitle>
              <CardDescription className="text-xs">
                控制LLM API调用行为
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0 space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="llm-simulation" className="text-sm">
                    模拟模式
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    使用模拟数据，不调用真实API
                  </p>
                </div>
                <Switch
                  id="llm-simulation"
                  checked={enableLLMSimulation}
                  onCheckedChange={toggleLLMSimulation}
                />
              </div>

              {enableLLMSimulation && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <Label className="text-sm flex items-center gap-2">
                      <Clock className="h-3 w-3" />
                      模拟延迟: {simulationDelay}ms
                    </Label>
                    <Slider
                      value={[simulationDelay]}
                      onValueChange={(value) => setSimulationDelay(value[0])}
                      max={10000}
                      min={500}
                      step={500}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>0.5s</span>
                      <span>10s</span>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* 调试设置 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Eye className="h-4 w-4" />
                调试设置
              </CardTitle>
              <CardDescription className="text-xs">
                控制调试信息的显示
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0 space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="detailed-logging" className="text-sm">
                    详细日志
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    在控制台显示详细的调试信息
                  </p>
                </div>
                <Switch
                  id="detailed-logging"
                  checked={enableDetailedLogging}
                  onCheckedChange={toggleDetailedLogging}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="debug-panel" className="text-sm">
                    调试面板
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    显示可视化调试面板
                  </p>
                </div>
                <Switch
                  id="debug-panel"
                  checked={showDebugPanel}
                  onCheckedChange={toggleDebugPanel}
                />
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={resetSettings}
              className="flex-1"
            >
              重置设置
            </Button>
            <Button
              size="sm"
              onClick={() => setIsOpen(false)}
              className="flex-1"
            >
              完成
            </Button>
          </div>

          {/* 状态指示 */}
          <div className="text-xs text-muted-foreground space-y-1">
            <div className="flex justify-between">
              <span>环境:</span>
              <Badge variant="outline" className="text-xs">
                {process.env.NODE_ENV}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>模式:</span>
              <Badge 
                variant={enableLLMSimulation ? "secondary" : "default"} 
                className="text-xs"
              >
                {enableLLMSimulation ? "模拟" : "真实API"}
              </Badge>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
