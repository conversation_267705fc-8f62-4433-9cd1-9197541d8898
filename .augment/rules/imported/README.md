---
type: "agent_requested"
description: "项目说明和工程结构以及技术选型说明"
---
# D2C

### 这是一个通过 LLM 将图片转化为代码的项目。
### 基本的工作流程：
1. 用户在 SectionA 上传设计图或者原型图
2. 用户可以在 SectionA 的聊天区描述页面逻辑，和相关要求
3. 发送后系统调用 LLM 结合内置提示词,设计图，用户描述，生成前端组件树(SectionB)和前端代码(SectionC)
3. 用户在 SectionB 上选择组件树节点，可以在 SectionA 的聊天区域引用后要求端 LLM 针对性修改代码

### 工程基础
#### 工程目录
```
v0-d2-c/
├── app/                          # Next.js App Router 目录
│   ├── globals.css              # 全局样式文件
│   ├── layout.tsx               # 根布局组件
│   └── page.tsx                 # 首页组件
├── components/                   # React 组件目录
│   ├── layout/                  # 布局相关组件
│   │   ├── header.tsx           # 页面头部组件
│   │   └── main-layout.tsx      # 主布局组件
│   ├── sections/                # 主要功能区域组件
│   │   ├── section-a/           # 区域A：图片上传和聊天功能
│   │   │   ├── chat-window.tsx  # 聊天窗口组件
│   │   │   ├── image-preview.tsx # 图片预览组件
│   │   │   ├── index.tsx        # 区域A主组件
│   │   │   └── project-settings.tsx # 项目设置组件
│   │   ├── section-b/           # 区域B：组件树展示
│   │   │   ├── component-tree.tsx # 组件树组件
│   │   │   └── index.tsx        # 区域B主组件
│   │   └── section-c/           # 区域C：代码预览和浏览器
│   │       ├── browser.tsx      # 浏览器预览组件
│   │       └── index.tsx        # 区域C主组件
│   ├── ui/                      # Shadcn UI 基础组件库
│   │   ├── accordion.tsx        # 手风琴组件
│   │   ├── alert-dialog.tsx     # 警告对话框组件
│   │   ├── alert.tsx            # 警告组件
│   │   ├── avatar.tsx           # 头像组件
│   │   ├── badge.tsx            # 徽章组件
│   │   ├── button.tsx           # 按钮组件
│   │   ├── card.tsx             # 卡片组件
│   │   ├── dialog.tsx           # 对话框组件
│   │   ├── input.tsx            # 输入框组件
│   │   ├── select.tsx           # 选择器组件
│   │   ├── table.tsx            # 表格组件
│   │   ├── tabs.tsx             # 标签页组件
│   │   └── ...                  # 其他UI组件
│   └── theme-provider.tsx       # 主题提供者组件
├── hooks/                       # 自定义React Hooks
│   ├── use-mobile.tsx           # 移动端检测Hook
│   └── use-toast.ts             # Toast通知Hook
├── lib/                         # 工具库
│   └── utils.ts                 # 通用工具函数
├── public/                      # 静态资源目录
│   ├── placeholder-logo.png     # 占位符Logo
│   ├── placeholder-logo.svg     # 占位符Logo SVG
│   ├── placeholder-user.jpg     # 占位符用户头像
│   ├── placeholder.jpg          # 占位符图片
│   └── placeholder.svg          # 占位符SVG
├── styles/                      # 样式文件目录
│   └── globals.css              # 全局样式
├── types/                       # TypeScript 类型定义
│   └── index.ts                 # 主要类型定义文件
├── components.json              # Shadcn UI 配置文件
├── next.config.mjs              # Next.js 配置文件
├── package.json                 # 项目依赖配置
├── postcss.config.mjs           # PostCSS 配置
├── tailwind.config.ts           # Tailwind CSS 配置
└── tsconfig.json                # TypeScript 配置
```

### 关键目录说明

#### `/app` - Next.js App Router
- 使用 Next.js 15 的 App Router 架构
- `layout.tsx`: 定义应用的根布局，包含主题提供者和全局样式
- `page.tsx`: 应用首页，整合三个主要功能区域
- `globals.css`: 全局CSS样式，包含Tailwind CSS基础样式

#### `/components/sections` - 核心功能区域
- **Section A**: 图片上传和AI对话区域
  - 支持设计图/原型图上传
  - 集成聊天界面，用户可描述需求
  - 项目设置和配置管理
- **Section B**: 组件树可视化区域
  - 展示LLM生成的前端组件结构
  - 支持节点选择和交互
  - 组件层级关系展示
- **Section C**: 代码预览和浏览器区域
  - 实时代码预览
  - 内置浏览器组件
  - 代码生成结果展示

#### `/components/ui` - Shadcn UI组件库
- 基于Radix UI构建的高质量组件库
- 包含40+个常用UI组件
- 支持主题切换和自定义样式
- 完全类型安全的TypeScript支持

#### `/hooks` - 自定义Hooks
- `use-mobile.tsx`: 响应式设计支持，检测移动端设备
- `use-toast.ts`: 全局通知系统

#### `/lib` - 工具库
- `utils.ts`: 包含常用工具函数，如类名合并、样式处理等

#### `/types` - 类型定义
- 集中管理TypeScript类型定义
- 确保类型安全和代码提示

### 技术栈
- **框架**: Next.js 15 (App Router)
- **UI库**: React 19 + Shadcn UI + Radix UI
- **样式**: Tailwind CSS + CSS Modules
- **语言**: TypeScript 5
- **状态管理**: React Hooks + Context API
- **图标**: Lucide React
- **表单**: React Hook Form + Zod验证
- **主题**: next-themes (支持暗色/亮色模式)
- **图表**: Recharts
- **通知**: Sonner

