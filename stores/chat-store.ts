import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { Message, MessageStatus, MessageType, ChatState, ChatActions, GenerationRequest } from '@/types'

type ChatStore = ChatState & ChatActions

export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => ({
      // 状态
      messages: [
        {
          id: "welcome-1",
          role: "assistant" as const,
          content: "你好！我可以帮你分析这张图片吗？",
          timestamp: new Date(),
          status: "sent" as MessageStatus,
          messageType: "text" as MessageType,
        }
      ],
      isLoading: false,
      error: null,

      // 操作方法
      addMessage: (content: string, role: "user" | "assistant", messageType: MessageType = "text") => {
        const newMessage: Message = {
          id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          role,
          content,
          timestamp: new Date(),
          status: role === "user" ? "sent" : "pending",
          messageType,
        }

        set((state) => ({
          messages: [...state.messages, newMessage],
          error: null,
        }))

        return newMessage.id
      },

      updateMessageStatus: (messageId: string, status: MessageStatus) => {
        set((state) => ({
          messages: state.messages.map((msg) =>
            msg.id === messageId ? { ...msg, status } : msg
          ),
        }))
      },

      clearMessages: () => {
        set({
          messages: [],
          error: null,
        })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      setError: (error: string | null) => {
        set({ error })
      },

      // 发送消息到LLM
      sendToLLM: async (request: GenerationRequest) => {
        set({ isLoading: true, error: null })

        try {
          // 添加用户消息（如果有新的聊天内容）
          const lastMessage = request.chatHistory[request.chatHistory.length - 1]
          if (lastMessage && lastMessage.role === 'user') {
            // 消息已经在chatHistory中，不需要重复添加
          }

          // 添加助手消息占位符
          const assistantMessageId = get().addMessage('', 'assistant', 'text')

          // 调用聊天API
          const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(request),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || 'API调用失败')
          }

          const result = await response.json()

          // 更新助手消息内容
          set((state) => ({
            messages: state.messages.map((msg) =>
              msg.id === assistantMessageId
                ? { ...msg, content: result.content, status: 'sent' as MessageStatus }
                : msg
            ),
            isLoading: false,
          }))

          return result
        } catch (error) {
          console.error('LLM调用失败:', error)
          const errorMessage = error instanceof Error ? error.message : 'LLM调用失败'

          set((state) => ({
            messages: state.messages.filter(msg => msg.content !== ''), // 移除空的助手消息
            error: errorMessage,
            isLoading: false,
          }))

          throw error
        }
      },

      // 流式发送消息到LLM
      sendToLLMStream: async (request: GenerationRequest, onChunk?: (chunk: string) => void) => {
        set({ isLoading: true, error: null })

        try {
          // 添加助手消息占位符
          const assistantMessageId = get().addMessage('', 'assistant', 'text')
          let accumulatedContent = ''

          // 调用流式聊天API
          const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'text/stream',
            },
            body: JSON.stringify(request),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || 'API调用失败')
          }

          const reader = response.body?.getReader()
          if (!reader) {
            throw new Error('无法读取响应流')
          }

          const decoder = new TextDecoder()

          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = decoder.decode(value)
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6)
                if (data === '[DONE]') {
                  set({ isLoading: false })
                  return accumulatedContent
                }

                try {
                  const parsed = JSON.parse(data)
                  if (parsed.content) {
                    accumulatedContent += parsed.content

                    // 更新消息内容
                    set((state) => ({
                      messages: state.messages.map((msg) =>
                        msg.id === assistantMessageId
                          ? { ...msg, content: accumulatedContent, status: 'sent' as MessageStatus }
                          : msg
                      ),
                    }))

                    // 调用回调函数
                    onChunk?.(parsed.content)
                  } else if (parsed.error) {
                    throw new Error(parsed.error)
                  }
                } catch (parseError) {
                  console.warn('解析流数据失败:', parseError)
                }
              }
            }
          }

          set({ isLoading: false })
          return accumulatedContent
        } catch (error) {
          console.error('LLM流式调用失败:', error)
          const errorMessage = error instanceof Error ? error.message : 'LLM流式调用失败'

          set((state) => ({
            messages: state.messages.filter(msg => msg.content !== ''), // 移除空的助手消息
            error: errorMessage,
            isLoading: false,
          }))

          throw error
        }
      },
    }),
    {
      name: 'chat-storage',
      // 只持久化消息，不持久化临时状态
      partialize: (state) => ({ 
        messages: state.messages.filter(msg => msg.id !== "welcome-1") // 不持久化欢迎消息
      }),
    }
  )
)

// 辅助函数：格式化时间戳
export const formatTimestamp = (timestamp: Date): string => {
  const now = new Date()
  const messageTime = new Date(timestamp)
  const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) {
    return "刚刚"
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`
  } else if (diffInMinutes < 24 * 60) {
    const hours = Math.floor(diffInMinutes / 60)
    return `${hours}小时前`
  } else {
    return messageTime.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}
