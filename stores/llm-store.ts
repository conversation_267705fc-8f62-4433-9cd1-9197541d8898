import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { LLMState, LLMActions, GenerationRequest, GenerationResponse, LLMModel } from '@/types'
import { SUPPORTED_MODELS, DEFAULT_MODEL } from '@/lib/llm-service'
import { globalRequestCancellation, generateRequestId } from '@/lib/request-cancellation'

type LLMStore = LLMState & LLMActions & {
  currentRequestId: string | null
}

export const useLLMStore = create<LLMStore>()(
  persist(
    (set, get) => ({
      // 状态
      generations: [],
      currentGeneration: null,
      isGenerating: false,
      error: null,
      models: SUPPORTED_MODELS,
      selectedModel: DEFAULT_MODEL,
      currentRequestId: null,

      // 操作方法
      generateCode: async (request: GenerationRequest) => {
        const requestId = generateRequestId('generate')
        set({ isGenerating: true, error: null, currentRequestId: requestId })

        try {
          // 使用选择的模型
          const enhancedRequest = {
            ...request,
            model: request.model || get().selectedModel,
          }

          // 创建可取消的请求
          const controller = globalRequestCancellation.createController(requestId)

          // 调用代码生成API
          const response = await fetch('/api/generate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(enhancedRequest),
            signal: controller.signal,
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || '代码生成失败')
          }

          const result = await response.json()

          // 创建生成响应对象
          const generation: GenerationResponse = {
            id: result.id || `gen-${Date.now()}`,
            content: result.content,
            model: result.model || enhancedRequest.model || DEFAULT_MODEL,
            timestamp: new Date(result.timestamp || Date.now()),
            usage: result.usage,
          }

          // 更新状态
          set((state) => ({
            generations: [generation, ...state.generations],
            currentGeneration: generation,
            isGenerating: false,
            currentRequestId: null,
          }))

          return generation
        } catch (error) {
          console.error('代码生成失败:', error)

          // 检查是否是取消错误
          if (error instanceof Error && error.name === 'AbortError') {
            set({
              isGenerating: false,
              currentRequestId: null,
              error: '代码生成已取消',
            })
            return
          }

          const errorMessage = error instanceof Error ? error.message : '代码生成失败'

          set({
            error: errorMessage,
            isGenerating: false,
            currentRequestId: null,
          })

          throw error
        } finally {
          globalRequestCancellation.cleanup(requestId)
        }
      },

      // 流式代码生成
      generateCodeStream: async (request: GenerationRequest, onChunk?: (chunk: string) => void) => {
        set({ isGenerating: true, error: null })
        
        try {
          // 使用选择的模型
          const enhancedRequest = {
            ...request,
            model: request.model || get().selectedModel,
          }

          // 创建临时生成对象
          const tempGeneration: GenerationResponse = {
            id: `gen-${Date.now()}`,
            content: '',
            model: enhancedRequest.model || DEFAULT_MODEL,
            timestamp: new Date(),
          }

          // 添加到生成列表
          set((state) => ({
            generations: [tempGeneration, ...state.generations],
            currentGeneration: tempGeneration,
          }))

          // 调用流式代码生成API
          const response = await fetch('/api/generate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'text/stream',
            },
            body: JSON.stringify(enhancedRequest),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || '代码生成失败')
          }

          const reader = response.body?.getReader()
          if (!reader) {
            throw new Error('无法读取响应流')
          }

          const decoder = new TextDecoder()
          let accumulatedContent = ''
          
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = decoder.decode(value)
            const lines = chunk.split('\n')
            
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6)
                if (data === '[DONE]') {
                  set({ isGenerating: false })
                  return accumulatedContent
                }
                
                try {
                  const parsed = JSON.parse(data)
                  if (parsed.content) {
                    accumulatedContent += parsed.content
                    
                    // 更新生成内容
                    set((state) => ({
                      generations: state.generations.map((gen) =>
                        gen.id === tempGeneration.id 
                          ? { ...gen, content: accumulatedContent }
                          : gen
                      ),
                      currentGeneration: state.currentGeneration?.id === tempGeneration.id
                        ? { ...state.currentGeneration, content: accumulatedContent }
                        : state.currentGeneration,
                    }))
                    
                    // 调用回调函数
                    onChunk?.(parsed.content)
                  } else if (parsed.error) {
                    throw new Error(parsed.error)
                  }
                } catch (parseError) {
                  console.warn('解析流数据失败:', parseError)
                }
              }
            }
          }
          
          set({ isGenerating: false })
          return accumulatedContent
        } catch (error) {
          console.error('流式代码生成失败:', error)
          const errorMessage = error instanceof Error ? error.message : '流式代码生成失败'
          
          set({
            error: errorMessage,
            isGenerating: false,
          })
          
          throw error
        }
      },

      cancelGeneration: () => {
        const { currentRequestId } = get()
        if (currentRequestId) {
          globalRequestCancellation.cancel(currentRequestId)
          set({
            isGenerating: false,
            currentRequestId: null,
            error: '代码生成已取消'
          })
        }
      },

      setCurrentGeneration: (generation) => {
        set({ currentGeneration: generation })
      },

      clearGenerations: () => {
        set({ 
          generations: [],
          currentGeneration: null,
          error: null,
        })
      },

      loadModels: async () => {
        try {
          const response = await fetch('/api/chat')
          if (response.ok) {
            const data = await response.json()
            if (data.models) {
              set({ models: data.models })
            }
          }
        } catch (error) {
          console.error('加载模型列表失败:', error)
          // 使用默认模型列表
          set({ models: SUPPORTED_MODELS })
        }
      },

      setSelectedModel: (model) => {
        set({ selectedModel: model })
      },

      setLoading: (loading) => {
        set({ isGenerating: loading })
      },

      setError: (error) => {
        set({ error })
      },
    }),
    {
      name: 'llm-storage',
      // 持久化生成历史和选择的模型
      partialize: (state) => ({ 
        generations: state.generations.slice(0, 10), // 只保留最近10个生成结果
        selectedModel: state.selectedModel,
      }),
    }
  )
)
