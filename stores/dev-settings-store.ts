import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface DevSettings {
  // 开发模式开关
  isDevelopmentMode: boolean
  // 是否启用LLM模拟模式（不实际调用API）
  enableLLMSimulation: boolean
  // 是否启用详细调试日志
  enableDetailedLogging: boolean
  // 是否显示调试面板
  showDebugPanel: boolean
  // 模拟延迟时间（毫秒）
  simulationDelay: number
}

interface DevSettingsActions {
  // 切换开发模式
  toggleDevelopmentMode: () => void
  // 切换LLM模拟模式
  toggleLLMSimulation: () => void
  // 切换详细日志
  toggleDetailedLogging: () => void
  // 切换调试面板
  toggleDebugPanel: () => void
  // 设置模拟延迟
  setSimulationDelay: (delay: number) => void
  // 重置所有设置
  resetSettings: () => void
}

type DevSettingsStore = DevSettings & DevSettingsActions

// 默认设置
const defaultSettings: DevSettings = {
  isDevelopmentMode: process.env.NODE_ENV === 'development',
  enableLLMSimulation: true, // 默认启用模拟模式，避免意外的API调用
  enableDetailedLogging: process.env.NODE_ENV === 'development',
  showDebugPanel: false,
  simulationDelay: 2000, // 2秒模拟延迟
}

export const useDevSettingsStore = create<DevSettingsStore>()(
  persist(
    (set, get) => ({
      ...defaultSettings,

      toggleDevelopmentMode: () => {
        set((state) => ({ 
          isDevelopmentMode: !state.isDevelopmentMode 
        }))
      },

      toggleLLMSimulation: () => {
        set((state) => ({ 
          enableLLMSimulation: !state.enableLLMSimulation 
        }))
      },

      toggleDetailedLogging: () => {
        set((state) => ({ 
          enableDetailedLogging: !state.enableDetailedLogging 
        }))
      },

      toggleDebugPanel: () => {
        set((state) => ({ 
          showDebugPanel: !state.showDebugPanel 
        }))
      },

      setSimulationDelay: (delay: number) => {
        set({ simulationDelay: Math.max(500, Math.min(10000, delay)) }) // 限制在0.5-10秒之间
      },

      resetSettings: () => {
        set(defaultSettings)
      },
    }),
    {
      name: 'dev-settings-storage',
      // 只在开发环境下持久化
      skipHydration: process.env.NODE_ENV === 'production',
    }
  )
)

// 调试日志工具函数
export const debugLog = {
  group: (title: string, data?: any) => {
    const { enableDetailedLogging } = useDevSettingsStore.getState()
    if (enableDetailedLogging) {
      console.group(`🔍 ${title}`)
      if (data) {
        console.log(data)
      }
    }
  },

  log: (message: string, data?: any) => {
    const { enableDetailedLogging } = useDevSettingsStore.getState()
    if (enableDetailedLogging) {
      console.log(`📝 ${message}`, data || '')
    }
  },

  error: (message: string, error?: any) => {
    const { enableDetailedLogging } = useDevSettingsStore.getState()
    if (enableDetailedLogging) {
      console.error(`❌ ${message}`, error || '')
    }
  },

  warn: (message: string, data?: any) => {
    const { enableDetailedLogging } = useDevSettingsStore.getState()
    if (enableDetailedLogging) {
      console.warn(`⚠️ ${message}`, data || '')
    }
  },

  success: (message: string, data?: any) => {
    const { enableDetailedLogging } = useDevSettingsStore.getState()
    if (enableDetailedLogging) {
      console.log(`✅ ${message}`, data || '')
    }
  },

  groupEnd: () => {
    const { enableDetailedLogging } = useDevSettingsStore.getState()
    if (enableDetailedLogging) {
      console.groupEnd()
    }
  },

  // 请求跟踪
  request: (requestId: string, url: string, data?: any) => {
    const { enableDetailedLogging } = useDevSettingsStore.getState()
    if (enableDetailedLogging) {
      console.group(`🚀 API请求 [${requestId}]`)
      console.log(`📍 URL: ${url}`)
      console.log(`📋 请求数据:`, data)
      console.log(`⏰ 时间: ${new Date().toISOString()}`)
      console.groupEnd()
    }
  },

  response: (requestId: string, status: number, data?: any, duration?: number) => {
    const { enableDetailedLogging } = useDevSettingsStore.getState()
    if (enableDetailedLogging) {
      console.group(`📥 API响应 [${requestId}]`)
      console.log(`📊 状态: ${status}`)
      console.log(`📋 响应数据:`, data)
      if (duration) {
        console.log(`⏱️ 耗时: ${duration}ms`)
      }
      console.log(`⏰ 时间: ${new Date().toISOString()}`)
      console.groupEnd()
    }
  },
}
