import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { toast } from 'sonner'
import { Project, ProjectConfig, ProjectState, ProjectActions } from '@/types'

type ProjectStore = ProjectState & ProjectActions

export const useProjectStore = create<ProjectStore>()(
  persist(
    (set, get) => ({
      // 状态
      projects: [],
      currentProject: null,
      isLoading: false,
      error: null,

      // 操作方法
      loadProjects: async () => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/projects')
          if (!response.ok) {
            throw new Error('获取项目列表失败')
          }
          const projects = await response.json()
          
          // 如果没有当前项目，设置第一个项目为当前项目
          const { currentProject } = get()
          if (!currentProject && projects.length > 0) {
            set({ 
              projects, 
              currentProject: projects[0],
              isLoading: false 
            })
          } else {
            set({ projects, isLoading: false })
          }
        } catch (error) {
          console.error('Error loading projects:', error)
          set({ 
            error: error instanceof Error ? error.message : '加载项目失败',
            isLoading: false 
          })
        }
      },

      createProject: async (projectData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/projects', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(projectData),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || '创建项目失败')
          }

          const newProject = await response.json()
          const { projects } = get()

          set({
            projects: [...projects, newProject],
            currentProject: newProject,
            isLoading: false
          })

          toast.success(`项目 "${newProject.name}" 创建成功`)
        } catch (error) {
          console.error('Error creating project:', error)
          const errorMessage = error instanceof Error ? error.message : '创建项目失败'
          set({
            error: errorMessage,
            isLoading: false
          })
          toast.error(errorMessage)
        }
      },

      updateProject: async (id, updates) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/projects/${id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(updates),
          })
          
          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || '更新项目失败')
          }
          
          const updatedProject = await response.json()
          const { projects, currentProject } = get()
          
          const newProjects = projects.map(p => 
            p.id === id ? updatedProject : p
          )
          
          set({
            projects: newProjects,
            currentProject: currentProject?.id === id ? updatedProject : currentProject,
            isLoading: false
          })

          toast.success(`项目 "${updatedProject.name}" 更新成功`)
        } catch (error) {
          console.error('Error updating project:', error)
          const errorMessage = error instanceof Error ? error.message : '更新项目失败'
          set({
            error: errorMessage,
            isLoading: false
          })
          toast.error(errorMessage)
        }
      },

      deleteProject: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/projects/${id}`, {
            method: 'DELETE',
          })
          
          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || '删除项目失败')
          }
          
          const { projects, currentProject } = get()
          const newProjects = projects.filter(p => p.id !== id)
          
          // 如果删除的是当前项目，选择第一个可用项目
          const newCurrentProject = currentProject?.id === id 
            ? (newProjects.length > 0 ? newProjects[0] : null)
            : currentProject
          
          set({
            projects: newProjects,
            currentProject: newCurrentProject,
            isLoading: false
          })

          toast.success('项目删除成功')
        } catch (error) {
          console.error('Error deleting project:', error)
          const errorMessage = error instanceof Error ? error.message : '删除项目失败'
          set({
            error: errorMessage,
            isLoading: false
          })
          toast.error(errorMessage)
        }
      },

      setCurrentProject: (project) => {
        set({ currentProject: project })
      },

      updateProjectConfig: async (id, config) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/projects/${id}/config`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(config),
          })
          
          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || '更新项目配置失败')
          }
          
          const updatedConfig = await response.json()
          const { projects, currentProject } = get()
          
          const newProjects = projects.map(p => 
            p.id === id ? { ...p, config: updatedConfig, updatedAt: new Date() } : p
          )
          
          set({ 
            projects: newProjects,
            currentProject: currentProject?.id === id 
              ? { ...currentProject, config: updatedConfig, updatedAt: new Date() }
              : currentProject,
            isLoading: false 
          })
        } catch (error) {
          console.error('Error updating project config:', error)
          set({ 
            error: error instanceof Error ? error.message : '更新项目配置失败',
            isLoading: false 
          })
        }
      },

      setLoading: (loading) => {
        set({ isLoading: loading })
      },

      setError: (error) => {
        set({ error })
      },
    }),
    {
      name: 'project-storage',
      // 只持久化当前项目，不持久化项目列表（从API获取）
      partialize: (state) => ({ 
        currentProject: state.currentProject 
      }),
    }
  )
)
