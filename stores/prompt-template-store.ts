import { create } from 'zustand'
import { toast } from 'sonner'
import { PromptTemplate, PromptTemplateState, PromptTemplateActions } from '@/types'

type PromptTemplateStore = PromptTemplateState & PromptTemplateActions

export const usePromptTemplateStore = create<PromptTemplateStore>()((set, get) => ({
  // 状态
  templates: [],
  isLoading: false,
  error: null,

  // 操作方法
  loadTemplates: async () => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch('/api/prompt-templates')
      if (!response.ok) {
        throw new Error('获取模板列表失败')
      }
      const templates = await response.json()
      set({ templates, isLoading: false })
    } catch (error) {
      console.error('Error loading templates:', error)
      set({ 
        error: error instanceof Error ? error.message : '加载模板失败',
        isLoading: false 
      })
    }
  },

  createTemplate: async (templateData) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch('/api/prompt-templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(templateData),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '创建模板失败')
      }
      
      const newTemplate = await response.json()
      const { templates } = get()
      
      set({
        templates: [...templates, newTemplate],
        isLoading: false
      })

      toast.success(`模板 "${newTemplate.name}" 创建成功`)
    } catch (error) {
      console.error('Error creating template:', error)
      const errorMessage = error instanceof Error ? error.message : '创建模板失败'
      set({
        error: errorMessage,
        isLoading: false
      })
      toast.error(errorMessage)
    }
  },

  updateTemplate: async (id, updates) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`/api/prompt-templates/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '更新模板失败')
      }
      
      const updatedTemplate = await response.json()
      const { templates } = get()
      
      const newTemplates = templates.map(t => 
        t.id === id ? updatedTemplate : t
      )
      
      set({ 
        templates: newTemplates,
        isLoading: false 
      })
    } catch (error) {
      console.error('Error updating template:', error)
      set({ 
        error: error instanceof Error ? error.message : '更新模板失败',
        isLoading: false 
      })
    }
  },

  deleteTemplate: async (id) => {
    set({ isLoading: true, error: null })
    try {
      const response = await fetch(`/api/prompt-templates/${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '删除模板失败')
      }
      
      const { templates } = get()
      const newTemplates = templates.filter(t => t.id !== id)
      
      set({ 
        templates: newTemplates,
        isLoading: false 
      })
    } catch (error) {
      console.error('Error deleting template:', error)
      set({ 
        error: error instanceof Error ? error.message : '删除模板失败',
        isLoading: false 
      })
    }
  },

  setLoading: (loading) => {
    set({ isLoading: loading })
  },

  setError: (error) => {
    set({ error })
  },
}))

// 辅助函数：获取默认模板
export const getDefaultTemplate = (templates: PromptTemplate[]) => {
  return templates.find(template => template.isDefault) || templates[0] || null
}
