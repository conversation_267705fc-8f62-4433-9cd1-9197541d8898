import { UploadedImage, UploadError } from "@/types"

// 支持的图片格式
export const SUPPORTED_IMAGE_TYPES = [
  "image/png",
  "image/jpeg", 
  "image/jpg"
]

// 最大文件大小 (10MB)
export const MAX_FILE_SIZE = 10 * 1024 * 1024

/**
 * 验证图片文件
 */
export function validateImageFile(file: File): UploadError | null {
  // 检查文件类型
  if (!SUPPORTED_IMAGE_TYPES.includes(file.type)) {
    return {
      type: "format",
      message: "不支持的文件格式，请上传 PNG、JPG 或 JPEG 格式的图片"
    }
  }

  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    return {
      type: "size", 
      message: "文件大小超过限制，请上传小于 10MB 的图片"
    }
  }

  return null
}

/**
 * 生成图片预览URL
 */
export function createImagePreviewUrl(file: File): string {
  return URL.createObjectURL(file)
}

/**
 * 清理图片预览URL
 */
export function revokeImagePreviewUrl(url: string): void {
  URL.revokeObjectURL(url)
}

/**
 * 生成唯一文件ID
 */
export function generateFileId(): string {
  return `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 创建上传图片对象
 */
export function createUploadedImage(file: File): UploadedImage {
  return {
    id: generateFileId(),
    file,
    url: createImagePreviewUrl(file),
    name: file.name,
    size: file.size,
    type: file.type,
    uploadedAt: new Date()
  }
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes"
  
  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

/**
 * 保存图片到本地存储
 */
export function saveImageToLocalStorage(image: UploadedImage): void {
  try {
    const savedImages = getSavedImages()
    savedImages.push({
      ...image,
      // 不保存 File 对象
      file: undefined as any
    })
    localStorage.setItem("uploaded_images", JSON.stringify(savedImages))
  } catch (error) {
    console.error("Failed to save image to localStorage:", error)
  }
}

/**
 * 从本地存储获取保存的图片
 */
export function getSavedImages(): Partial<UploadedImage>[] {
  try {
    const saved = localStorage.getItem("uploaded_images")
    return saved ? JSON.parse(saved) : []
  } catch (error) {
    console.error("Failed to load images from localStorage:", error)
    return []
  }
}

/**
 * 从本地存储删除图片
 */
export function removeImageFromLocalStorage(imageId: string): void {
  try {
    const savedImages = getSavedImages()
    const filtered = savedImages.filter(img => img.id !== imageId)
    localStorage.setItem("uploaded_images", JSON.stringify(filtered))
  } catch (error) {
    console.error("Failed to remove image from localStorage:", error)
  }
}
