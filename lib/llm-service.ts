import OpenAI from 'openai'
import { LLMRequest, GenerationRequest, GenerationResponse } from '@/types'
import { withRetry, createAPIError, formatErrorMessage, isRetryableError } from './error-handling'

// OpenRouter配置
// 创建OpenAI客户端的工厂函数，确保在服务端正确访问环境变量
const createOpenAIClient = () => {
  const apiKey = process.env.OPENROUTER_API_KEY
  const baseURL = process.env.OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1'



  if (!apiKey) {
    throw new Error('OPENROUTER_API_KEY environment variable is missing. Please set it in your .env.local file.')
  }

  return new OpenAI({
    baseURL,
    apiKey,
    defaultHeaders: {
      'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
      'X-Title': 'D2C - Design to Code Tool',
    },
  })
}

// 默认模型配置
export const DEFAULT_MODEL = process.env.OPENROUTER_DEFAULT_MODEL || 'anthropic/claude-sonnet-4'

// 支持的模型列表
export const SUPPORTED_MODELS = [
  {
    id: 'anthropic/claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    provider: 'Anthropic',
    maxTokens: 8192,
    supportsVision: true,
  },
  {
    id: 'openai/gpt-4o',
    name: 'GPT-4o',
    provider: 'OpenAI',
    maxTokens: 4096,
    supportsVision: true,
  },
  {
    id: 'openai/gpt-4o-mini',
    name: 'GPT-4o Mini',
    provider: 'OpenAI',
    maxTokens: 16384,
    supportsVision: true,
  },
  {
    id: 'google/gemini-pro-1.5',
    name: 'Gemini Pro 1.5',
    provider: 'Google',
    maxTokens: 8192,
    supportsVision: true,
  },
]

// 构建LLM请求消息
export function buildLLMMessages(request: GenerationRequest): LLMRequest['messages'] {
  const messages: LLMRequest['messages'] = []

  // 添加系统提示词
  if (request.promptTemplate) {
    messages.push({
      role: 'system',
      content: request.promptTemplate,
    })
  }

  // 添加聊天历史
  request.chatHistory.forEach((msg) => {
    if (msg.role === 'user' || msg.role === 'assistant') {
      messages.push({
        role: msg.role,
        content: msg.content,
      })
    }
  })

  // 如果有图片，添加到最后一条用户消息中
  if ((request.imageBase64 || request.imageUrl) && messages.length > 0) {
    const lastUserMessageIndex = messages.map((m, i) => ({ ...m, index: i }))
      .filter(m => m.role === 'user')
      .pop()?.index

    if (lastUserMessageIndex !== undefined) {
      const lastMessage = messages[lastUserMessageIndex]

      // 优先使用base64编码，如果没有则使用URL
      const imageData = request.imageBase64 || request.imageUrl

      if (imageData) {
        messages[lastUserMessageIndex] = {
          role: 'user',
          content: [
            {
              type: 'text',
              text: typeof lastMessage.content === 'string' ? lastMessage.content : '',
            },
            {
              type: 'image_url',
              image_url: {
                url: imageData,
              },
            },
          ],
        }
      }
    }
  }

  return messages
}

// 调用LLM API
export async function callLLM(request: GenerationRequest): Promise<GenerationResponse> {
  return withRetry(async () => {
    try {
      const messages = buildLLMMessages(request)
      const model = request.model || DEFAULT_MODEL

      const openai = createOpenAIClient()
      const response = await openai.chat.completions.create({
        model,
        messages: messages as any, // 临时类型转换，OpenAI库的类型定义较严格
        max_tokens: 4096,
        temperature: 0.7,
        stream: false,
      })

      if (!response.choices || response.choices.length === 0) {
        throw createAPIError('LLM返回空响应', 500, 'EMPTY_RESPONSE')
      }

      const generationResponse: GenerationResponse = {
        id: response.id,
        content: response.choices[0]?.message?.content || '',
        model: response.model,
        timestamp: new Date(),
        usage: response.usage ? {
          prompt_tokens: response.usage.prompt_tokens,
          completion_tokens: response.usage.completion_tokens,
          total_tokens: response.usage.total_tokens,
        } : undefined,
      }

      return generationResponse
    } catch (error) {
      console.error('LLM API调用失败:', error)

      // 转换OpenAI错误为标准API错误
      if (error instanceof Error) {
        const message = formatErrorMessage(error)
        throw createAPIError(`LLM API调用失败: ${message}`, undefined, 'LLM_ERROR', error)
      }

      throw createAPIError('LLM API调用失败: 未知错误', 500, 'UNKNOWN_ERROR')
    }
  }, {
    maxRetries: 3,
    baseDelay: 1000,
    timeout: 60000, // 60秒超时
  })
}

// 流式调用LLM API
export async function* callLLMStream(request: GenerationRequest): AsyncGenerator<string, void, unknown> {
  let retryCount = 0
  const maxRetries = 3

  while (retryCount <= maxRetries) {
    try {
      const messages = buildLLMMessages(request)
      const model = request.model || DEFAULT_MODEL

      const openai = createOpenAIClient()
      const stream = await openai.chat.completions.create({
        model,
        messages: messages as any, // 临时类型转换
        max_tokens: 4096,
        temperature: 0.7,
        stream: true,
      })

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content
        if (content) {
          yield content
        }
      }

      // 成功完成，退出重试循环
      return
    } catch (error) {
      console.error(`LLM流式API调用失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error)

      retryCount++

      // 如果达到最大重试次数，抛出错误
      if (retryCount > maxRetries) {
        const message = formatErrorMessage(error)
        throw createAPIError(`LLM流式API调用失败: ${message}`, undefined, 'LLM_STREAM_ERROR', error)
      }

      // 检查是否可重试
      if (!isRetryableError(error)) {
        const message = formatErrorMessage(error)
        throw createAPIError(`LLM流式API调用失败: ${message}`, undefined, 'LLM_STREAM_ERROR', error)
      }

      // 等待后重试
      const delayMs = 1000 * Math.pow(2, retryCount - 1) // 指数退避
      await new Promise(resolve => setTimeout(resolve, delayMs))
    }
  }
}

// 验证API密钥
export async function validateAPIKey(): Promise<boolean> {
  try {
    const openai = createOpenAIClient()
    await openai.models.list()
    return true
  } catch (error) {
    console.error('API密钥验证失败:', error)
    return false
  }
}
