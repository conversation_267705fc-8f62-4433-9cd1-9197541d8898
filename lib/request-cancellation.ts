/**
 * 请求取消管理工具库
 */

export class RequestCancellation {
  private controllers: Map<string, AbortController> = new Map()
  private timeouts: Map<string, NodeJS.Timeout> = new Map()

  /**
   * 创建一个新的请求控制器
   */
  createController(requestId: string): AbortController {
    // 如果已存在，先取消之前的请求
    this.cancel(requestId)
    
    const controller = new AbortController()
    this.controllers.set(requestId, controller)
    
    return controller
  }

  /**
   * 取消指定的请求
   */
  cancel(requestId: string): boolean {
    const controller = this.controllers.get(requestId)
    const timeout = this.timeouts.get(requestId)
    
    if (controller) {
      controller.abort()
      this.controllers.delete(requestId)
    }
    
    if (timeout) {
      clearTimeout(timeout)
      this.timeouts.delete(requestId)
    }
    
    return !!(controller || timeout)
  }

  /**
   * 取消所有请求
   */
  cancelAll(): void {
    for (const [requestId] of this.controllers) {
      this.cancel(requestId)
    }
  }

  /**
   * 检查请求是否已被取消
   */
  isCancelled(requestId: string): boolean {
    const controller = this.controllers.get(requestId)
    return controller ? controller.signal.aborted : false
  }

  /**
   * 获取请求的AbortSignal
   */
  getSignal(requestId: string): AbortSignal | undefined {
    const controller = this.controllers.get(requestId)
    return controller?.signal
  }

  /**
   * 设置请求超时
   */
  setTimeout(requestId: string, timeoutMs: number): void {
    const timeout = setTimeout(() => {
      this.cancel(requestId)
    }, timeoutMs)
    
    this.timeouts.set(requestId, timeout)
  }

  /**
   * 清理已完成的请求
   */
  cleanup(requestId: string): void {
    this.controllers.delete(requestId)
    const timeout = this.timeouts.get(requestId)
    if (timeout) {
      clearTimeout(timeout)
      this.timeouts.delete(requestId)
    }
  }

  /**
   * 获取活跃请求数量
   */
  getActiveRequestCount(): number {
    return this.controllers.size
  }

  /**
   * 获取所有活跃请求ID
   */
  getActiveRequestIds(): string[] {
    return Array.from(this.controllers.keys())
  }
}

// 全局请求取消管理器
export const globalRequestCancellation = new RequestCancellation()

/**
 * 可取消的fetch包装器
 */
export async function cancellableFetch(
  requestId: string,
  url: string,
  options: RequestInit = {},
  timeoutMs?: number
): Promise<Response> {
  const controller = globalRequestCancellation.createController(requestId)
  
  // 设置超时
  if (timeoutMs) {
    globalRequestCancellation.setTimeout(requestId, timeoutMs)
  }
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    })
    
    return response
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('请求已被取消')
    }
    throw error
  } finally {
    globalRequestCancellation.cleanup(requestId)
  }
}

/**
 * 可取消的流式请求
 */
export async function* cancellableStreamFetch(
  requestId: string,
  url: string,
  options: RequestInit = {},
  timeoutMs?: number
): AsyncGenerator<string, void, unknown> {
  const controller = globalRequestCancellation.createController(requestId)
  
  // 设置超时
  if (timeoutMs) {
    globalRequestCancellation.setTimeout(requestId, timeoutMs)
  }
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('无法读取响应流')
    }
    
    const decoder = new TextDecoder()
    
    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break
        
        // 检查是否被取消
        if (controller.signal.aborted) {
          throw new Error('请求已被取消')
        }
        
        const chunk = decoder.decode(value, { stream: true })
        yield chunk
      }
    } finally {
      reader.releaseLock()
    }
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('请求已被取消')
    }
    throw error
  } finally {
    globalRequestCancellation.cleanup(requestId)
  }
}

/**
 * React Hook for request cancellation
 */
export function useRequestCancellation() {
  const createCancellableRequest = (requestId: string) => {
    return {
      cancel: () => globalRequestCancellation.cancel(requestId),
      isCancelled: () => globalRequestCancellation.isCancelled(requestId),
      getSignal: () => globalRequestCancellation.getSignal(requestId),
    }
  }

  const cancelAll = () => {
    globalRequestCancellation.cancelAll()
  }

  const getActiveRequestCount = () => {
    return globalRequestCancellation.getActiveRequestCount()
  }

  return {
    createCancellableRequest,
    cancelAll,
    getActiveRequestCount,
  }
}

/**
 * 生成唯一的请求ID
 */
export function generateRequestId(prefix = 'req'): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}
