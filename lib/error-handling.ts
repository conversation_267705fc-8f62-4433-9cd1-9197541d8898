/**
 * 错误处理和重试机制工具库
 */

export interface RetryOptions {
  maxRetries?: number
  baseDelay?: number
  maxDelay?: number
  backoffFactor?: number
  timeout?: number
}

export interface APIError extends Error {
  status?: number
  code?: string
  details?: any
}

// 默认重试配置
const DEFAULT_RETRY_OPTIONS: Required<RetryOptions> = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  timeout: 30000,
}

/**
 * 创建API错误对象
 */
export function createAPIError(
  message: string,
  status?: number,
  code?: string,
  details?: any
): APIError {
  const error = new Error(message) as APIError
  error.status = status
  error.code = code
  error.details = details
  return error
}

/**
 * 检查错误是否可重试
 */
export function isRetryableError(error: unknown): boolean {
  if (error instanceof Error) {
    const apiError = error as APIError
    
    // 网络错误通常可重试
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return true
    }
    
    // 特定HTTP状态码可重试
    if (apiError.status) {
      const retryableStatuses = [408, 429, 500, 502, 503, 504]
      return retryableStatuses.includes(apiError.status)
    }
    
    // 超时错误可重试
    if (error.message.includes('timeout') || error.message.includes('TIMEOUT')) {
      return true
    }
  }
  
  return false
}

/**
 * 计算重试延迟时间（指数退避）
 */
export function calculateDelay(
  attempt: number,
  options: RetryOptions = {}
): number {
  const { baseDelay, maxDelay, backoffFactor } = {
    ...DEFAULT_RETRY_OPTIONS,
    ...options,
  }
  
  const delay = baseDelay * Math.pow(backoffFactor, attempt - 1)
  return Math.min(delay, maxDelay)
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 带超时的Promise包装器
 */
export function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  timeoutMessage = '请求超时'
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(createAPIError(timeoutMessage, 408, 'TIMEOUT')), timeoutMs)
    ),
  ])
}

/**
 * 重试装饰器函数
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const config = { ...DEFAULT_RETRY_OPTIONS, ...options }
  let lastError: unknown
  
  for (let attempt = 1; attempt <= config.maxRetries + 1; attempt++) {
    try {
      // 为每次尝试添加超时
      const result = await withTimeout(fn(), config.timeout)
      return result
    } catch (error) {
      lastError = error
      
      // 如果是最后一次尝试，直接抛出错误
      if (attempt > config.maxRetries) {
        break
      }
      
      // 检查是否可重试
      if (!isRetryableError(error)) {
        break
      }
      
      // 计算延迟时间并等待
      const delayMs = calculateDelay(attempt, options)
      console.warn(`API调用失败，${delayMs}ms后进行第${attempt}次重试:`, error)
      await delay(delayMs)
    }
  }
  
  throw lastError
}

/**
 * 格式化错误消息
 */
export function formatErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    const apiError = error as APIError
    
    // 根据错误类型返回用户友好的消息
    if (apiError.status === 401) {
      return 'API密钥无效，请检查配置'
    }
    
    if (apiError.status === 403) {
      return '访问被拒绝，请检查API权限'
    }
    
    if (apiError.status === 429) {
      return '请求过于频繁，请稍后再试'
    }
    
    if (apiError.status === 500) {
      return '服务器内部错误，请稍后重试'
    }
    
    if (apiError.status === 503) {
      return '服务暂时不可用，请稍后重试'
    }
    
    if (apiError.code === 'TIMEOUT') {
      return '请求超时，请检查网络连接'
    }
    
    if (error.message.includes('fetch')) {
      return '网络连接失败，请检查网络设置'
    }
    
    return error.message
  }
  
  if (typeof error === 'string') {
    return error
  }
  
  return '未知错误'
}

/**
 * 错误分类
 */
export enum ErrorCategory {
  NETWORK = 'network',
  AUTH = 'auth',
  RATE_LIMIT = 'rate_limit',
  SERVER = 'server',
  CLIENT = 'client',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown',
}

/**
 * 获取错误分类
 */
export function getErrorCategory(error: unknown): ErrorCategory {
  if (error instanceof Error) {
    const apiError = error as APIError
    
    if (apiError.code === 'TIMEOUT' || error.message.includes('timeout')) {
      return ErrorCategory.TIMEOUT
    }
    
    if (apiError.status === 401 || apiError.status === 403) {
      return ErrorCategory.AUTH
    }
    
    if (apiError.status === 429) {
      return ErrorCategory.RATE_LIMIT
    }
    
    if (apiError.status && apiError.status >= 500) {
      return ErrorCategory.SERVER
    }
    
    if (apiError.status && apiError.status >= 400) {
      return ErrorCategory.CLIENT
    }
    
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return ErrorCategory.NETWORK
    }
  }
  
  return ErrorCategory.UNKNOWN
}

/**
 * 获取错误的建议操作
 */
export function getErrorSuggestion(error: unknown): string {
  const category = getErrorCategory(error)
  
  switch (category) {
    case ErrorCategory.NETWORK:
      return '请检查网络连接并重试'
    case ErrorCategory.AUTH:
      return '请检查API密钥配置'
    case ErrorCategory.RATE_LIMIT:
      return '请求过于频繁，请稍后再试'
    case ErrorCategory.SERVER:
      return '服务器暂时不可用，请稍后重试'
    case ErrorCategory.TIMEOUT:
      return '请求超时，请检查网络或稍后重试'
    case ErrorCategory.CLIENT:
      return '请求参数有误，请检查输入'
    default:
      return '请稍后重试或联系技术支持'
  }
}
