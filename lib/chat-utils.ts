import { GenerationRequest, Message, UploadedImage, PromptTemplate, Project } from '@/types'

/**
 * 构建LLM生成请求
 * 组合图片、提示词模板和聊天历史
 */
export function buildGenerationRequest(
  chatHistory: Message[],
  uploadedImage?: UploadedImage | null,
  currentProject?: Project | null,
  promptTemplate?: PromptTemplate | null,
  selectedModel?: string
): GenerationRequest {
  // 优先使用base64编码，如果没有则使用URL
  const imageBase64 = uploadedImage?.base64
  const imageUrl = !imageBase64 && uploadedImage ?
    (uploadedImage.url.startsWith('http') ?
      uploadedImage.url :
      `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}${uploadedImage.url}`
    ) : undefined

  // 获取提示词模板内容
  let promptTemplateContent: string | undefined

  if (promptTemplate) {
    promptTemplateContent = promptTemplate.content
  } else if (currentProject?.config?.promptTemplateId) {
    // 如果项目配置了提示词模板ID，但没有传入模板对象，需要从API获取
    // 这里暂时使用undefined，调用方应该确保传入正确的模板
    promptTemplateContent = undefined
  }

  return {
    imageUrl,
    imageBase64,
    promptTemplate: promptTemplateContent,
    chatHistory,
    model: selectedModel,
  }
}

/**
 * 获取当前项目的提示词模板
 */
export async function getCurrentProjectPromptTemplate(
  currentProject: Project | null,
  templates: PromptTemplate[]
): Promise<PromptTemplate | null> {
  if (!currentProject?.config?.promptTemplateId) {
    return null
  }

  // 从本地模板列表中查找
  const template = templates.find(t => t.id === currentProject.config.promptTemplateId)
  if (template) {
    return template
  }

  // 如果本地没有找到，从API获取
  try {
    const response = await fetch(`/api/prompt-templates/${currentProject.config.promptTemplateId}`)
    if (response.ok) {
      return await response.json()
    }
  } catch (error) {
    console.error('获取提示词模板失败:', error)
  }

  return null
}

/**
 * 验证生成请求的有效性
 */
export function validateGenerationRequest(request: GenerationRequest): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  // 检查聊天历史
  if (!request.chatHistory || request.chatHistory.length === 0) {
    errors.push('聊天历史不能为空')
  }

  // 检查是否有用户消息
  const hasUserMessage = request.chatHistory.some(msg => msg.role === 'user')
  if (!hasUserMessage) {
    errors.push('至少需要一条用户消息')
  }

  // 检查图片数据格式（如果提供）
  if (request.imageBase64) {
    // 验证base64格式
    if (!request.imageBase64.startsWith('data:image/')) {
      errors.push('图片base64格式无效')
    }
  } else if (request.imageUrl) {
    try {
      new URL(request.imageUrl)
    } catch {
      // 如果不是完整URL，检查是否是相对路径
      if (!request.imageUrl.startsWith('/')) {
        errors.push('图片URL格式无效')
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

/**
 * 格式化错误消息
 */
export function formatErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  
  if (typeof error === 'string') {
    return error
  }
  
  return '未知错误'
}

/**
 * 检查是否支持图片的模型
 */
export function isVisionModel(modelId: string): boolean {
  const visionModels = [
    'anthropic/claude-3.5-sonnet',
    'openai/gpt-4o',
    'openai/gpt-4o-mini',
    'google/gemini-pro-1.5',
  ]
  
  return visionModels.includes(modelId)
}

/**
 * 获取模型的显示名称
 */
export function getModelDisplayName(modelId: string): string {
  const modelNames: Record<string, string> = {
    'anthropic/claude-3.5-sonnet': 'Claude 3.5 Sonnet',
    'openai/gpt-4o': 'GPT-4o',
    'openai/gpt-4o-mini': 'GPT-4o Mini',
    'google/gemini-pro-1.5': 'Gemini Pro 1.5',
  }
  
  return modelNames[modelId] || modelId
}
