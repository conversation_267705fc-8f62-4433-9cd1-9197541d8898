/**
 * 图片Base64编码工具库
 */

/**
 * 将File对象转换为base64编码
 */
export async function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result)
      } else {
        reject(new Error('Failed to convert file to base64'))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('Error reading file'))
    }
    
    reader.readAsDataURL(file)
  })
}

/**
 * 将图片URL转换为base64编码
 */
export async function imageUrlToBase64(url: string): Promise<string> {
  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`)
    }
    
    const blob = await response.blob()
    return blobToBase64(blob)
  } catch (error) {
    console.error('Error converting image URL to base64:', error)
    throw error
  }
}

/**
 * 将Blob对象转换为base64编码
 */
export async function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result)
      } else {
        reject(new Error('Failed to convert blob to base64'))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('Error reading blob'))
    }
    
    reader.readAsDataURL(blob)
  })
}

/**
 * 从base64字符串中提取MIME类型
 */
export function getMimeTypeFromBase64(base64: string): string | null {
  const match = base64.match(/^data:([^;]+);base64,/)
  return match ? match[1] : null
}

/**
 * 验证base64字符串是否为有效的图片
 */
export function isValidImageBase64(base64: string): boolean {
  const mimeType = getMimeTypeFromBase64(base64)
  if (!mimeType) return false
  
  const validImageTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp'
  ]
  
  return validImageTypes.includes(mimeType.toLowerCase())
}

/**
 * 压缩base64图片（如果图片过大）
 */
export async function compressBase64Image(
  base64: string, 
  maxWidth: number = 1024, 
  maxHeight: number = 1024, 
  quality: number = 0.8
): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      if (!ctx) {
        reject(new Error('Failed to get canvas context'))
        return
      }
      
      // 计算新的尺寸
      let { width, height } = img
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height)
        width *= ratio
        height *= ratio
      }
      
      canvas.width = width
      canvas.height = height
      
      // 绘制压缩后的图片
      ctx.drawImage(img, 0, 0, width, height)
      
      // 获取压缩后的base64
      const mimeType = getMimeTypeFromBase64(base64) || 'image/jpeg'
      const compressedBase64 = canvas.toDataURL(mimeType, quality)
      
      resolve(compressedBase64)
    }
    
    img.onerror = () => {
      reject(new Error('Failed to load image for compression'))
    }
    
    img.src = base64
  })
}

/**
 * 获取base64图片的尺寸信息
 */
export async function getBase64ImageDimensions(base64: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      })
    }
    
    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }
    
    img.src = base64
  })
}

/**
 * 计算base64字符串的大小（字节）
 */
export function getBase64Size(base64: string): number {
  // 移除data:image/...;base64,前缀
  const base64Data = base64.split(',')[1] || base64
  
  // Base64编码后的大小约为原始大小的4/3
  // 但需要考虑padding字符
  const padding = (base64Data.match(/=/g) || []).length
  return Math.floor((base64Data.length * 3) / 4) - padding
}

/**
 * 格式化文件大小显示
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
